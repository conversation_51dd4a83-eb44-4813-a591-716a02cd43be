package com.worklink.formsservice.api;


import com.worklink.formsservice.dto.hmrc.HmrcDto;
import com.worklink.formsservice.dto.workerapplication.WorkerApplicationCreateDto;
import com.worklink.formsservice.model.Hmrc;
import com.worklink.formsservice.model.Occupational;
import com.worklink.formsservice.service.HmrcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.net.URI;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class HmrcController {


    private final HmrcService hmrcService;

    public HmrcController(HmrcService hmrcService) {
        this.hmrcService = hmrcService;
    }

    @PostMapping(value = "hmrc", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<HmrcDto> createHmrc(@RequestBody HmrcDto hmrcCreateDto) {
        log.info("Request to add hmrc with : {}", hmrcCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
//        hmrcService.addHmrc(hmrcCreateDto);
        return ResponseEntity.ok( hmrcService.addHmrc(hmrcCreateDto));
    }




    /*@ViewServices*/
    @GetMapping(value = "hmrc/{workerId}")
    public ResponseEntity<HmrcDto> findHmrcs
    (
            @PathVariable("workerId") Long workerId
    ){
        log.info("Request to get worker  hmrc for worker_id: {}", workerId);
        return ResponseEntity.ok(hmrcService.findHmrcs(workerId));
    }


    @GetMapping(value = "hmrc/{workerId}/{agencyId}")
    public ResponseEntity<HmrcDto> findAgencyHmrcs
            (
                    @PathVariable("workerId") Long workerId,
                    @PathVariable("agencyId") Long agencyId
            ){
        log.info("Request to get worker  workerTraining for worker_id: {}", workerId);
        return ResponseEntity.ok(hmrcService.findAgencyWorkerHmrcs(workerId,agencyId));
    }

    @GetMapping(value = "hmrc/worker/{id}")
    public ResponseEntity<Hmrc> findByWorkerId(@PathVariable("id") Long id) {
        log.info("Request to get hmrc with id : {}", id);
        return ResponseEntity.ok(hmrcService.findByWorkerId(id));
    }


    @DeleteMapping(value = "hmrc/{id}")
    public ResponseEntity deleteHmrc(@PathVariable("id") Long id) {
        hmrcService.deleteHmrc(id);
        return ResponseEntity.noContent().build();
    }



    @PostMapping(value = "hmrc/signature-upload",consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity uploadSignature(@RequestParam("file") MultipartFile file,
                                                 @RequestParam("workerId") Long workerId
    ){

        log.info("Request to add worker profile image : {}");
        hmrcService.addSignature( workerId, file);
        return  ResponseEntity.noContent().build();
    }



    @PutMapping(value = "hmrc", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<HmrcDto> reviewHmrc(@RequestBody HmrcDto hmrcCreateDto) {
        log.info("Request to review hmrc with : {}", hmrcCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        hmrcService.updateHmrc(hmrcCreateDto);
        return ResponseEntity.created(uri)
                .build();
    }

}

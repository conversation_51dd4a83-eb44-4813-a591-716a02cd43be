package com.worklink.formsservice.service;


import com.worklink.formsservice.dto.education.EducationCreateDto;
import com.worklink.formsservice.dto.education.EducationsCreateDto;
import com.worklink.formsservice.dto.education.IEducationResultDto;
import com.worklink.formsservice.dto.employer.EmployersCreateDto;
import com.worklink.formsservice.dto.employer.IEmployerResultDto;
import com.worklink.formsservice.dto.employment.EmploymentsCreateDto;
import com.worklink.formsservice.dto.employment.IEmploymentResultDto;
import com.worklink.formsservice.dto.reference.IReferenceResultDto;
import com.worklink.formsservice.dto.reference.ReferencesCreateDto;
import com.worklink.formsservice.dto.workerapplication.WorkerApplicationCreateDto;
import com.worklink.formsservice.dto.workerapplication.WorkerApplicationResultDto;
import com.worklink.formsservice.dto.workerapplication.WorkerApplicationUpdateDto;
import com.worklink.formsservice.model.WorkerApplication;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface WorkerApplicationService {
    WorkerApplicationResultDto addWorkerApplication(WorkerApplicationCreateDto workerApplicationCreateDto);

    void addGeneralSignature(Long workerId, MultipartFile files);


    void deleteWorkerApplication(Long id);

    WorkerApplication findById(Long id);

    WorkerApplicationResultDto findWorkerApplications(Long workerId);


    WorkerApplicationResultDto findAgencyWorkerApplication(Long workerId, Long agencyId);

    List<WorkerApplicationResultDto> findAgencyWorkerForms(Long workerId, Long agencyId, PageRequest of);

    Page<WorkerApplication> findAllPaged(PageRequest of);

    WorkerApplication save(WorkerApplicationUpdateDto workerApplicationUpdateDto);

    WorkerApplication getOne(Long id);

    void addApplicationDoc(Long workerId, Long compliance, MultipartFile file);


    List<IEmployerResultDto> findWorkerEmployers(Long workerId);
    void deleteEmployer(Long id);

    List<IEducationResultDto> findWorkerEducations(Long workerId);
    void deleteEducation(Long id);

    List<IEmploymentResultDto> findWorkerEmployments(Long workerId);

    List<IReferenceResultDto> findWorkerReferences(Long workerId);


    void addSafeguardingSignature(Long workerId, MultipartFile file);

    void updateWorkerApplication(WorkerApplicationCreateDto occupational);
}

spring:
  http:
    multipart:
      max-file-size: 2MB
      max-request-size: 10MB
  sleuth:
    sampler:
      probability: 1
zuul:
  sensitiveHeaders: <PERSON><PERSON>,Set-Cookie
server:
  port: 8765
eureka:
  instance:
    leaseRenewalIntervalInSeconds: 1
    leaseExpirationDurationInSeconds: 2
    prefer-ip-address: true
  client:
    registerWithEureka: true
    fetchRegistry: true
    healthcheck:
      enabled: true
    lease:
      duration: 5
    serviceUrl:
      defaultZone: http://*************:8761/eureka/


hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 60000


ribbon:
  ReadTimeout: 60000
  ConnectTimeout: 60000

  management:
    security:
      enabled: false

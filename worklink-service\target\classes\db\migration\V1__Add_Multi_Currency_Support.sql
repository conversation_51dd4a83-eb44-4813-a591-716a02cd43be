-- Migration script to add multi-currency support
-- This script adds currency tables and updates existing tables with currency fields

-- Create Currency table
CREATE TABLE currency (
    code VARCHAR(3) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    symbol VARCHAR(5),
    active BOOLEAN NOT NULL DEFAULT TRUE,
    decimal_places INT NOT NULL DEFAULT 2
);

-- Create Exchange Rate table
CREATE TABLE exchange_rate (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    from_currency VARCHAR(3) NOT NULL,
    to_currency VARCHAR(3) NOT NULL,
    rate DECIMAL(19,6) NOT NULL,
    rate_date DATETIME NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    source VARCHAR(50) DEFAULT 'STRIPE',
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    UNIQUE KEY unique_rate_per_date (from_currency, to_currency, rate_date),
    INDEX idx_from_to_currency (from_currency, to_currency),
    INDEX idx_rate_date (rate_date),
    INDEX idx_active (is_active)
);

-- Add base_currency column to agency table
ALTER TABLE agency ADD COLUMN base_currency VARCHAR(3) DEFAULT 'USD';

-- Add preferred_currency column to users table (if it exists in this service)
-- Note: This might need to be run in the user-service database as well
-- ALTER TABLE users ADD COLUMN preferred_currency VARCHAR(3) DEFAULT 'USD';

-- Insert common currencies
INSERT INTO currency (code, name, symbol, active, decimal_places) VALUES
('USD', 'US Dollar', '$', TRUE, 2),
('EUR', 'Euro', '€', TRUE, 2),
('GBP', 'British Pound', '£', TRUE, 2),
('CAD', 'Canadian Dollar', 'C$', TRUE, 2),
('AUD', 'Australian Dollar', 'A$', TRUE, 2),
('JPY', 'Japanese Yen', '¥', TRUE, 0),
('CHF', 'Swiss Franc', 'CHF', TRUE, 2),
('CNY', 'Chinese Yuan', '¥', TRUE, 2),
('SEK', 'Swedish Krona', 'kr', TRUE, 2),
('NOK', 'Norwegian Krone', 'kr', TRUE, 2),
('DKK', 'Danish Krone', 'kr', TRUE, 2),
('PLN', 'Polish Zloty', 'zł', TRUE, 2),
('CZK', 'Czech Koruna', 'Kč', TRUE, 2),
('HUF', 'Hungarian Forint', 'Ft', TRUE, 0),
('RON', 'Romanian Leu', 'lei', TRUE, 2),
('BGN', 'Bulgarian Lev', 'лв', TRUE, 2),
('HRK', 'Croatian Kuna', 'kn', TRUE, 2),
('RUB', 'Russian Ruble', '₽', TRUE, 2),
('TRY', 'Turkish Lira', '₺', TRUE, 2),
('BRL', 'Brazilian Real', 'R$', TRUE, 2),
('MXN', 'Mexican Peso', '$', TRUE, 2),
('INR', 'Indian Rupee', '₹', TRUE, 2),
('KRW', 'South Korean Won', '₩', TRUE, 0),
('SGD', 'Singapore Dollar', 'S$', TRUE, 2),
('HKD', 'Hong Kong Dollar', 'HK$', TRUE, 2),
('NZD', 'New Zealand Dollar', 'NZ$', TRUE, 2),
('ZAR', 'South African Rand', 'R', TRUE, 2),
('THB', 'Thai Baht', '฿', TRUE, 2),
('MYR', 'Malaysian Ringgit', 'RM', TRUE, 2),
('PHP', 'Philippine Peso', '₱', TRUE, 2),
('IDR', 'Indonesian Rupiah', 'Rp', TRUE, 0),
('VND', 'Vietnamese Dong', '₫', TRUE, 0);

-- Add foreign key constraint for agency base_currency
ALTER TABLE agency ADD CONSTRAINT fk_agency_base_currency 
    FOREIGN KEY (base_currency) REFERENCES currency(code);

-- Create indexes for better performance
CREATE INDEX idx_agency_base_currency ON agency(base_currency);

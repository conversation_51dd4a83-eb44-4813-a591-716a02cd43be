import { Currency } from "@/common/models";

/**
 * Currency formatting utilities for multi-currency support
 */

// Common currencies with their formatting options
export const CURRENCY_CONFIGS: Record<string, Intl.NumberFormatOptions> = {
  USD: { style: "currency", currency: "USD", minimumFractionDigits: 2 },
  EUR: { style: "currency", currency: "EUR", minimumFractionDigits: 2 },
  GBP: { style: "currency", currency: "GBP", minimumFractionDigits: 2 },
  CAD: { style: "currency", currency: "CAD", minimumFractionDigits: 2 },
  AUD: { style: "currency", currency: "AUD", minimumFractionDigits: 2 },
  JPY: { style: "currency", currency: "JPY", minimumFractionDigits: 0 },
  CHF: { style: "currency", currency: "CHF", minimumFractionDigits: 2 },
  CNY: { style: "currency", currency: "CNY", minimumFractionDigits: 2 },
  SEK: { style: "currency", currency: "SEK", minimumFractionDigits: 2 },
  NOK: { style: "currency", currency: "NOK", minimumFractionDigits: 2 },
  DKK: { style: "currency", currency: "DKK", minimumFractionDigits: 2 },
  PLN: { style: "currency", currency: "PLN", minimumFractionDigits: 2 },
  CZK: { style: "currency", currency: "CZK", minimumFractionDigits: 2 },
  HUF: { style: "currency", currency: "HUF", minimumFractionDigits: 0 },
  RON: { style: "currency", currency: "RON", minimumFractionDigits: 2 },
  BGN: { style: "currency", currency: "BGN", minimumFractionDigits: 2 },
  HRK: { style: "currency", currency: "HRK", minimumFractionDigits: 2 },
  RUB: { style: "currency", currency: "RUB", minimumFractionDigits: 2 },
  TRY: { style: "currency", currency: "TRY", minimumFractionDigits: 2 },
  BRL: { style: "currency", currency: "BRL", minimumFractionDigits: 2 },
  MXN: { style: "currency", currency: "MXN", minimumFractionDigits: 2 },
  INR: { style: "currency", currency: "INR", minimumFractionDigits: 2 },
  KRW: { style: "currency", currency: "KRW", minimumFractionDigits: 0 },
  SGD: { style: "currency", currency: "SGD", minimumFractionDigits: 2 },
  HKD: { style: "currency", currency: "HKD", minimumFractionDigits: 2 },
  NZD: { style: "currency", currency: "NZD", minimumFractionDigits: 2 },
  ZAR: { style: "currency", currency: "ZAR", minimumFractionDigits: 2 },
  THB: { style: "currency", currency: "THB", minimumFractionDigits: 2 },
  MYR: { style: "currency", currency: "MYR", minimumFractionDigits: 2 },
  PHP: { style: "currency", currency: "PHP", minimumFractionDigits: 2 },
  IDR: { style: "currency", currency: "IDR", minimumFractionDigits: 0 },
  VND: { style: "currency", currency: "VND", minimumFractionDigits: 0 },
};

// Default locale for currency formatting
const DEFAULT_LOCALE = "en-US";

/**
 * Create a currency formatter for a specific currency
 */
export function createCurrencyFormatter(
  currencyCode: string,
  locale: string = DEFAULT_LOCALE
): Intl.NumberFormat {
  const config = CURRENCY_CONFIGS[currencyCode] || {
    style: "currency",
    currency: currencyCode,
    minimumFractionDigits: 2,
  };

  return new Intl.NumberFormat(locale, config);
}

/**
 * Format an amount in a specific currency
 */
export function formatCurrency(
  amount: number,
  currencyCode: string,
  locale: string = DEFAULT_LOCALE
): string {
  const formatter = createCurrencyFormatter(currencyCode, locale);
  return formatter.format(amount);
}

/**
 * Format an amount with a fallback to USD if currency is not supported
 */
export function formatCurrencyWithFallback(
  amount: number,
  currencyCode?: string,
  locale: string = DEFAULT_LOCALE
): string {
  const currency = currencyCode || "USD";
  return formatCurrency(amount, currency, locale);
}

/**
 * Get currency symbol for a currency code
 */
export function getCurrencySymbol(currencyCode: string): string {
  try {
    const formatter = createCurrencyFormatter(currencyCode);
    const parts = formatter.formatToParts(0);
    const symbolPart = parts.find((part) => part.type === "currency");
    return symbolPart?.value || currencyCode;
  } catch {
    return currencyCode;
  }
}

/**
 * Convert amount from one currency to another using exchange rate
 */
export function convertCurrency(
  amount: number,
  fromCurrency: string,
  toCurrency: string,
  exchangeRate: number
): number {
  if (fromCurrency === toCurrency) {
    return amount;
  }
  return amount * exchangeRate;
}

/**
 * Format converted currency with exchange rate info
 */
export function formatConvertedCurrency(
  amount: number,
  fromCurrency: string,
  toCurrency: string,
  exchangeRate: number,
  locale: string = DEFAULT_LOCALE
): {
  originalAmount: string;
  convertedAmount: string;
  exchangeRate: number;
} {
  const convertedAmount = convertCurrency(amount, fromCurrency, toCurrency, exchangeRate);

  return {
    originalAmount: formatCurrency(amount, fromCurrency, locale),
    convertedAmount: formatCurrency(convertedAmount, toCurrency, locale),
    exchangeRate,
  };
}

/**
 * Get decimal places for a currency
 */
export function getCurrencyDecimalPlaces(currencyCode: string): number {
  const config = CURRENCY_CONFIGS[currencyCode];
  return config?.minimumFractionDigits ?? 2;
}

/**
 * Validate currency code format (ISO 4217)
 */
export function isValidCurrencyCode(code: string): boolean {
  return /^[A-Z]{3}$/.test(code);
}

/**
 * Get user's preferred currency from localStorage or default to USD
 */
export function getUserPreferredCurrency(): string {
  if (typeof window !== "undefined") {
    return localStorage.getItem("preferredCurrency") || "USD";
  }
  return "USD";
}

/**
 * Set user's preferred currency in localStorage
 */
export function setUserPreferredCurrency(currencyCode: string): void {
  if (typeof window !== "undefined" && isValidCurrencyCode(currencyCode)) {
    localStorage.setItem("preferredCurrency", currencyCode);
  }
}

/**
 * Legacy USD formatter for backward compatibility
 */
export const usdFormatter = createCurrencyFormatter("USD");

/**
 * Create a dynamic currency formatter based on user preference
 */
export function createUserCurrencyFormatter(): Intl.NumberFormat {
  const preferredCurrency = getUserPreferredCurrency();
  return createCurrencyFormatter(preferredCurrency);
}

/**
 * Format amount using user's preferred currency
 */
export function formatUserCurrency(amount: number): string {
  const preferredCurrency = getUserPreferredCurrency();
  return formatCurrency(amount, preferredCurrency);
}

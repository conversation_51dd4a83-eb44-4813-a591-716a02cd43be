<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.17.0.final using JasperReports Library version 6.17.0-6d93193241dd8cc42629e188b94f9e0bc5722efd  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Invoice" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="4eedbb89-b4f6-4469-9ab6-f642a1688cf7">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="DataAdapter.xml"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="Title" forecolor="#FFFFFF" fontSize="50" isBold="false" pdfFontName="Times-Bold"/>
	<style name="SubTitle" forecolor="#CCCCCC" fontSize="18" isBold="false" pdfFontName="Times-Roman"/>
	<style name="Column header" forecolor="#666666" fontSize="14" isBold="true"/>
	<style name="Detail" mode="Transparent"/>
	<style name="Row" mode="Transparent" pdfFontName="Times-Roman">
		<conditionalStyle>
			<conditionExpression><![CDATA[$V{REPORT_COUNT}%2 == 0]]></conditionExpression>
			<style mode="Opaque" backcolor="#EEEFF0"/>
		</conditionalStyle>
	</style>
	<style name="Table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TH" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#CACED0">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$V{REPORT_COUNT}%2 == 0]]></conditionExpression>
			<style backcolor="#D8D8D8"/>
		</conditionalStyle>
	</style>
	<subDataset name="tableDataset" uuid="f13e6d36-5148-4ecc-bbe3-3035def80980">
		<queryString>
			<![CDATA[]]>
		</queryString>
	</subDataset>
	<parameter name="INVOICE_ID" class="java.lang.Long">
		<parameterDescription><![CDATA[Invoice]]></parameterDescription>
	</parameter>
	<queryString language="SQL">
		<![CDATA[select d.name       as agencyName,
       c.name       as clientName,
       c.first_line as clientFirstLine,
       c.town       as clientTown,
       c.county     as clientCounty,
       d.first_line as agentFirstLine,
       d.town       as agentTown,
       d.county     as agentCountry,
       total_amount as total_amount,
       invoice_status,
       invoice_id,
       sum(number_of_hours_worked) as number_of_hours_worked,
       ac.code      as assignmentCode,
       sum(total)        as lineItemTotal,
       invoice_date,
       shift_id,
       vat_percentage,
       vat_amount,
       sub_total_amount

from invoice_item
         inner join invoice on invoice_item.invoice_id = invoice.id
         inner join client c on invoice.client_id = c.id
         inner join agency d on invoice.agent_id = d.id
         left join shift s on s.id = invoice_item.shift_id
         left join assignment_code ac on s.assignment_code_id = ac.id
where invoice_id = $P{INVOICE_ID}
group by d.name,
         c.name,
         c.first_line,
         c.town,
         c.county,
         d.first_line,
         d.town,
         d.county,
         invoice_status,
         invoice_id,
         ac.code,
         invoice_date,
         shift_id,
         vat_percentage,
         vat_amount,
         sub_total_amount]]>
	</queryString>
	<field name="agencyName" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="agencyName"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="agency"/>
	</field>
	<field name="clientName" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="name"/>
		<property name="com.jaspersoft.studio.field.label" value="clientName"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="client"/>
	</field>
	<field name="clientFirstLine" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="first_line"/>
		<property name="com.jaspersoft.studio.field.label" value="clientFirstLine"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="client"/>
	</field>
	<field name="clientTown" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="town"/>
		<property name="com.jaspersoft.studio.field.label" value="clientTown"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="client"/>
	</field>
	<field name="clientCounty" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="county"/>
		<property name="com.jaspersoft.studio.field.label" value="clientCounty"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="client"/>
	</field>
	<field name="agentFirstLine" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="first_line"/>
		<property name="com.jaspersoft.studio.field.label" value="agentFirstLine"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="agency"/>
	</field>
	<field name="agentTown" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="town"/>
		<property name="com.jaspersoft.studio.field.label" value="agentTown"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="agency"/>
	</field>
	<field name="agentCountry" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="county"/>
		<property name="com.jaspersoft.studio.field.label" value="agentCountry"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="agency"/>
	</field>
	<field name="total_amount" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.name" value="total_amount"/>
		<property name="com.jaspersoft.studio.field.label" value="total_amount"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="invoice"/>
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="invoice_status" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="invoice_status"/>
		<property name="com.jaspersoft.studio.field.label" value="invoice_status"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="invoice"/>
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="invoice_id" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.name" value="invoice_id"/>
		<property name="com.jaspersoft.studio.field.label" value="invoice_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="invoice_item"/>
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="number_of_hours_worked" class="java.lang.Double">
		<property name="com.jaspersoft.studio.field.name" value="number_of_hours_worked"/>
		<property name="com.jaspersoft.studio.field.label" value="number_of_hours_worked"/>
	</field>
	<field name="assignmentCode" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="code"/>
		<property name="com.jaspersoft.studio.field.label" value="assignmentCode"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="assignment_code"/>
	</field>
	<field name="lineItemTotal" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.name" value="lineItemTotal"/>
		<property name="com.jaspersoft.studio.field.label" value="lineItemTotal"/>
	</field>
	<field name="invoice_date" class="java.sql.Date">
		<property name="com.jaspersoft.studio.field.name" value="invoice_date"/>
		<property name="com.jaspersoft.studio.field.label" value="invoice_date"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="invoice"/>
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="shift_id" class="java.lang.Long">
		<property name="com.jaspersoft.studio.field.name" value="shift_id"/>
		<property name="com.jaspersoft.studio.field.label" value="shift_id"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="invoice_item"/>
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="vat_percentage" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.name" value="vat_percentage"/>
		<property name="com.jaspersoft.studio.field.label" value="vat_percentage"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="invoice"/>
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="vat_amount" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.name" value="vat_amount"/>
		<property name="com.jaspersoft.studio.field.label" value="vat_amount"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="invoice"/>
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="sub_total_amount" class="java.math.BigDecimal">
		<property name="com.jaspersoft.studio.field.name" value="sub_total_amount"/>
		<property name="com.jaspersoft.studio.field.label" value="sub_total_amount"/>
		<property name="com.jaspersoft.studio.field.tree.path" value="invoice"/>
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<title>
		<band height="132" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="1" width="103" height="30" uuid="49222398-cce6-4ba7-82d9-cf105c25e30b"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[INVOICE]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="41" width="60" height="18" forecolor="#000000" uuid="1f828c59-2877-4aa5-9759-b26ba4db5bc4"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<text><![CDATA[Debit Note]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="59" width="60" height="18" forecolor="#000000" uuid="10099db9-a5b5-448c-8ae8-75a435e89f60"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<text><![CDATA[Issue Date:]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="77" width="60" height="18" forecolor="#000000" uuid="6e933fd8-7fad-4f8c-b116-002bd65ba787"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<text><![CDATA[Due Date:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="60" y="41" width="64" height="18" forecolor="#000000" uuid="c58a3cac-6c2b-4967-b8a5-a25404025e36"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{invoice_id}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="60" y="59" width="64" height="18" forecolor="#000000" uuid="fdf5bd16-614a-4c9f-a97c-9503e5e07313"/>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{invoice_date}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="390" y="40" width="156" height="20" forecolor="#000000" uuid="e5482e56-b992-4a06-8b00-7ad1430698ee">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="SansSerif" size="12"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Bill To]]></text>
			</staticText>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" x="390" y="60" width="156" height="16" forecolor="#005DFF" uuid="c0656117-623a-4258-a053-f3515ead2989">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" size="12"/>
					<paragraph leftIndent="2" rightIndent="3"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{clientName}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" x="390" y="76" width="156" height="16" forecolor="#000000" uuid="d9eb6c69-bbef-4dfc-bc9f-bea368ea2fe8">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" size="12"/>
					<paragraph leftIndent="2" rightIndent="3"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{clientFirstLine}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" x="390" y="92" width="156" height="16" forecolor="#000000" uuid="d68f7feb-6481-4b17-988b-8777e01172b9">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" size="12"/>
					<paragraph leftIndent="2" rightIndent="3"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{clientTown}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" x="390" y="108" width="156" height="16" forecolor="#000000" uuid="aaf4f641-ed22-4a29-bef4-866ad61dfe51">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
					<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="SansSerif" size="12"/>
					<paragraph leftIndent="2" rightIndent="3"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{clientCounty}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="180" y="40" width="160" height="18" forecolor="#000000" uuid="54e50874-2b0a-4134-a5d3-14ea0ccdd9cb">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="SansSerif" size="12"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<text><![CDATA[Bill From]]></text>
			</staticText>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" x="180" y="58" width="160" height="16" forecolor="#005DFF" uuid="b65ed9d3-b42b-4f4d-9257-59a14e186a2e">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="SansSerif" size="12"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{agencyName}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" x="180" y="74" width="160" height="16" forecolor="#000000" uuid="07674f75-405c-4eef-9a22-af0b4b5ff4bb">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="SansSerif" size="12"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{agentFirstLine}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" x="180" y="90" width="160" height="16" forecolor="#000000" uuid="eb70e800-90cb-4820-8ae8-2eef0d99e80f">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="SansSerif" size="12"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{agentTown}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" x="180" y="106" width="160" height="16" forecolor="#000000" uuid="ddb5b8ed-a61b-41d8-b50b-63403b075ac9">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="SansSerif" size="12"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{agentCountry}]]></textFieldExpression>
			</textField>
			<textField pattern="MMMMM dd, yyyy">
				<reportElement x="60" y="77" width="120" height="18" uuid="cf619635-74a8-473e-9987-e89250bac0e1">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle" markup="none">
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="32">
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="100" height="32" forecolor="#7A0159" backcolor="#FA8C91" uuid="*************-416f-8b70-020c680def70"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#7A0159"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#7A0159"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#7A0159"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#7A0159"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font size="12" isBold="true"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<text><![CDATA[SHIFT ID]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="100" y="0" width="160" height="32" forecolor="#7A0159" backcolor="#FA8C91" uuid="55d90bef-bb88-4580-99b6-6be604465899"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#7A0159"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#7A0159"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#7A0159"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#7A0159"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font size="12" isBold="true"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<text><![CDATA[ASSIGNMENT CODE]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="260" y="0" width="170" height="32" forecolor="#7A0159" backcolor="#FA8C91" uuid="f465220a-e807-49eb-939e-59ac31c1b57a"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#7A0159"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#7A0159"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#7A0159"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#7A0159"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font size="12" isBold="true"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<text><![CDATA[UNITS]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="430" y="0" width="126" height="32" forecolor="#7A0159" backcolor="#FA8C91" uuid="8a6bdfa2-d676-484d-89a3-e0d24f8c3781">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#7A0159"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#7A0159"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#7A0159"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#7A0159"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font size="12" isBold="true"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<text><![CDATA[TOTAL   £]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="19">
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="0" width="100" height="19" uuid="661e60e1-adf9-42f1-b117-502d3249edbf">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font size="12"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{shift_id}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="100" y="0" width="160" height="19" uuid="e7913730-da93-4b63-bccf-7c02ea86f523">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font size="12"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{assignmentCode}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="260" y="0" width="170" height="19" uuid="d1c7ac3c-012a-441a-98a0-f3bccafddc82">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font size="12"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[java.math.BigDecimal.valueOf($F{number_of_hours_worked}).setScale(2, java.math.RoundingMode.HALF_UP)]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="430" y="0" width="126" height="19" uuid="2f46a307-fc3b-4e38-88bd-7c77238c3e0e">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font size="12"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{lineItemTotal}.setScale(2, java.math.RoundingMode.HALF_UP)]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="90">
			<textField isBlankWhenNull="true">
				<reportElement x="430" y="0" width="126" height="20" uuid="9616854f-eb32-48bd-a8b9-c73d5d00176f">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font size="12"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{sub_total_amount}.setScale(2, java.math.RoundingMode.HALF_UP)]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="280" y="1" width="150" height="20" forecolor="#005DFF" uuid="0600e248-d989-449b-8934-fb25f7f06945">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
				</reportElement>
				<box>
					<pen lineColor="#000000"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="12" isBold="false"/>
					<paragraph leftIndent="0" rightIndent="5"/>
				</textElement>
				<text><![CDATA[Sub Total]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="430" y="20" width="126" height="20" uuid="d46cfa9a-920f-425d-a681-120cf163a4bc">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font size="12"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{vat_amount}.compareTo( java.math.BigDecimal.ZERO )!=0?$F{vat_amount}.setScale(2, java.math.RoundingMode.HALF_UP): java.math.BigDecimal.ZERO]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="430" y="40" width="126" height="30" forecolor="#005DFF" uuid="3f26b49c-0153-4600-84cd-98a898f92f9b">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
				</reportElement>
				<box>
					<pen lineColor="#000000"/>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font size="12" isBold="true"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{total_amount}.setScale(2, java.math.RoundingMode.HALF_UP)]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="280" y="41" width="150" height="30" forecolor="#005DFF" uuid="2a411a40-1060-4493-89eb-469d794d91a4">
					<property name="com.jaspersoft.studio.unit.rightIndent" value="px"/>
				</reportElement>
				<box>
					<pen lineColor="#000000"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="12" isBold="false"/>
					<paragraph leftIndent="0" rightIndent="5"/>
				</textElement>
				<text><![CDATA[Total Due]]></text>
			</staticText>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement x="380" y="21" width="50" height="20" forecolor="#005DFF" uuid="d7fc4bdf-2450-4fb2-8608-a67fa4fda084">
					<property name="com.jaspersoft.studio.unit.leftIndent" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="12" isBold="true"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{vat_percentage}.equals( null ) ? "0 %"  : $F{vat_percentage} + "%"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="280" y="21" width="100" height="20" forecolor="#005DFF" uuid="139f5bc8-1ea4-4c3d-ac34-b4bc96c08b8e"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="12"/>
				</textElement>
				<text><![CDATA[VAT]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>

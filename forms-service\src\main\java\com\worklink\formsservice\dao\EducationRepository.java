package com.worklink.formsservice.dao;


import com.worklink.formsservice.dto.education.IEducationResultDto;
import com.worklink.formsservice.model.Education;
import com.worklink.formsservice.model.Education;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface EducationRepository extends JpaRepository<Education, Long> {

    List<IEducationResultDto> findAllByWorkerApplicationId(Long workerApplicationId);
}

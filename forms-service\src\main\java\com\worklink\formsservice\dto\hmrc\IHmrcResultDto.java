package com.worklink.formsservice.dto.hmrc;

import java.time.LocalDate;

public interface IHmrcResultDto {

    Long getId();
    String getLastname();

    String getFirstname();
    String getAddress();
    String getInsuarance();
    String getLastModifiedDate();
    LocalDate getEmploymentStart();
    String getSex();
    String getDob();
    Boolean getOtherJob();
    Boolean getPayments();
    Boolean getApril();
    String getStatement();
    Boolean getLoan();
    Boolean getApplyStatement();
    String getPlan();
    Boolean getSigned();
    LocalDate getSignDate();
    String getFullname();
    String getSubmitted();
}

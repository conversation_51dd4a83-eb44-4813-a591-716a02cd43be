package com.worklink.formsservice.mapper.workerapplication;


import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import com.worklink.formsservice.dao.WorkerApplicationRepository;
import com.worklink.formsservice.dto.workerapplication.WorkerApplicationResultDto;
import com.worklink.formsservice.model.Reference;
import com.worklink.formsservice.model.WorkerApplication;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.stream.Collectors;

@Component
@Slf4j
public class WorkerApplicationToWorkerApplicationResultDto implements Converter<WorkerApplication, WorkerApplicationResultDto> {

    private final WorkerApplicationRepository workerApplicationRepository;

    public WorkerApplicationToWorkerApplicationResultDto(WorkerApplicationRepository workerApplicationRepository) {
        this.workerApplicationRepository = workerApplicationRepository;
    }

    @Override
    public WorkerApplicationResultDto convert(WorkerApplication workerApplication) {
        WorkerApplicationResultDto workerApplicationResultDto = new WorkerApplicationResultDto();
        workerApplicationResultDto.setId(workerApplication.getId());

        DateTimeFormatter pattern = DateTimeFormatter.ofPattern("dd/MM/yyyy");

        workerApplicationResultDto.setWorkerId(workerApplication.getWorkerId());
        workerApplicationResultDto.setPosition(workerApplication.getPosition());
        workerApplicationResultDto.setLocation(workerApplication.getLocation());
        workerApplicationResultDto.setHours(workerApplication.getHours());
        workerApplicationResultDto.setClosingDate(workerApplication.getClosingDate()!=null ?workerApplication.getClosingDate():null);
        workerApplicationResultDto.setTitle(workerApplication.getTitle());
        workerApplicationResultDto.setLastname(workerApplication.getLastname());
        workerApplicationResultDto.setFirstname(workerApplication.getFirstname());
        workerApplicationResultDto.setPreferredName(workerApplication.getPreferredName());
//        workerApplicationResultDto.setNationality(workerApplication.getNationality());
        workerApplicationResultDto.setDob(workerApplication.getDob());
        workerApplicationResultDto.setAddress(workerApplication.getAddress());
        workerApplicationResultDto.setEmail(workerApplication.getEmail());
        workerApplicationResultDto.setPhone(workerApplication.getPhone());
        workerApplicationResultDto.setUkWork(workerApplication.getUkWork());
        workerApplicationResultDto.setRestrictionRes(workerApplication.getRestrictionRes());
        workerApplicationResultDto.setInsuaranceNum(workerApplication.getInsuaranceNum());
        workerApplicationResultDto.setPersonalState(workerApplication.getPersonalState());
        workerApplicationResultDto.setOffensesRes(workerApplication.getOffensesRes());
        workerApplicationResultDto.setInvestigationsRes(workerApplication.getInvestigationsRes());
        workerApplicationResultDto.setAvailability(workerApplication.getAvailability());
        workerApplicationResultDto.setDbsCert(workerApplication.getDbsCert());

        workerApplicationResultDto.setDbsRenewal(workerApplication.getDbsRenewal());
        workerApplicationResultDto.setDbs(workerApplication.getDbs());
        workerApplicationResultDto.setConfidential(workerApplication.getConfidential());

        workerApplicationResultDto.setSigned(workerApplication.getSigned());
        workerApplicationResultDto.setPrintName(workerApplication.getPrintName());
        workerApplicationResultDto.setSignDate(workerApplication.getSignDate());
        workerApplicationResultDto.setLicence(workerApplication.getLicence());
        workerApplicationResultDto.setCar(workerApplication.getCar());
        workerApplicationResultDto.setOnlyEmployment(workerApplication.getOnlyEmployment());
        workerApplicationResultDto.setSanctionsRes(workerApplication.getSanctionsRes());
        workerApplicationResultDto.setDismissedRes(workerApplication.getDismissedRes());
        workerApplicationResultDto.setRelatedRes(workerApplication.getRelatedRes());
        workerApplicationResultDto.setGenFullname(workerApplication.getGenFullname());
        workerApplicationResultDto.setGenSigned(workerApplication.getGenSigned());
        workerApplicationResultDto.setGenDate(workerApplication.getGenDate());
        workerApplicationResultDto.setUnavailable(workerApplication.getUnavailable());
        workerApplicationResultDto.setSubmitted(workerApplication.getSubmitted());

        workerApplicationResultDto.setRestriction(workerApplication.getRestriction());
        workerApplicationResultDto.setOffenses(workerApplication.getOffenses());
        workerApplicationResultDto.setInvestigations(workerApplication.getInvestigations());
        workerApplicationResultDto.setAvailability(workerApplication.getAvailability());
        workerApplicationResultDto.setSanctions(workerApplication.getSanctions());
        workerApplicationResultDto.setDismissed(workerApplication.getDismissed());
        workerApplicationResultDto.setRelated(workerApplication.getRelated());

        workerApplicationResultDto.setProfreg(workerApplication.getProfreg());
        workerApplicationResultDto.setCertificate(workerApplication.getCertificate());

        workerApplicationResultDto.setLastModifiedDate(workerApplication.getLastModifiedDate1());

        workerApplicationResultDto.setDbsNumber(workerApplication.getDbsNumber());
        workerApplicationResultDto.setDbsType(workerApplication.getDbsType());

        workerApplicationResultDto.setReferences(new ArrayList<>(workerApplication.getReferences()));
        workerApplicationResultDto.setEmployments(new ArrayList<>(workerApplication.getEmployments()));
        workerApplicationResultDto.setEmployers(new ArrayList<>(workerApplication.getEmployers()));
        workerApplicationResultDto.setEducations(new ArrayList<>(workerApplication.getEducations()));

        return workerApplicationResultDto;
    }

    @Override
    public JavaType getInputType(TypeFactory typeFactory) {
        return null;
    }

    @Override
    public JavaType getOutputType(TypeFactory typeFactory) {
        return null;
    }
}

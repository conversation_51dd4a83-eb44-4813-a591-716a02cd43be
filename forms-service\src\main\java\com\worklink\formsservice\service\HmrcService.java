package com.worklink.formsservice.service;


import com.worklink.formsservice.dto.hmrc.HmrcDto;
import com.worklink.formsservice.dto.hmrc.HmrcDto;
//import com.worklink.formsservice.model.Bank;
import com.worklink.formsservice.model.Hmrc;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface HmrcService {
    HmrcDto addHmrc(HmrcDto hmrcCreateDto);

    void updateHmrc(HmrcDto hmrcUpdateDto);

    void deleteHmrc(Long id);


    HmrcDto findHmrcs(Long workerId);


    Hmrc findByWorkerId(Long id);

    HmrcDto findAgencyWorkerHmrcs(Long workerId, Long agencyId);

    Page<Hmrc> findAllPaged(PageRequest of);

    Hmrc save(HmrcDto hmrcUpdateDto);

    Hmrc getOne(Long id);

    void addApplicationDoc(Long workerId, Long compliance, MultipartFile file);


    void addSignature(Long workerId, MultipartFile file);
}

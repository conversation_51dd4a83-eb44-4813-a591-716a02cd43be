spring:
  application:
    name: oauth-service
  sleuth:
    sampler:
      probability: 1
  main:
    allow-bean-definition-overriding: true
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: **************************************************************************************************************************************************************************************************************************************************************
    username: 'root'
    password: 'skdcnwauicn2ucnaecasdsajdnizucawencascdc'
    driver-class-name: org.mariadb.jdbc.Driver
    hikari:
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
        useLocalSessionState: true
        rewriteBatchedStatements: true
        cacheResultSetMetadata: true
        cacheServerConfiguration: true
        elideSetAutoCommits: true
        maintainTimeStats: false
    initialization-mode: always
  jpa:
    database-platform: org.hibernate.dialect.MariaDB103Dialect
    database: MYSQL
    show-sql: true
    hibernate:
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
      ddl-auto: none

    properties:
      hibernate.id.new_generator_mappings: true
      hibernate.connection.provider_disables_autocommit: true
      hibernate.cache.use_second_level_cache: false
      hibernate.cache.use_query_cache: false
      hibernate.generate_statistics: false
      hibernate.jdbc.batch_size: 500
      hibernate.order_inserts: true
      hibernate.order_updates: true
      hibernate.query.fail_on_pagination_over_collection_fetch: true
      hibernate.query.in_clause_parameter_padding: true
  data:
    jpa:
      repositories:
        bootstrap-mode: lazy

  jackson:
    serialization:
      fail-on-empty-beans: false
springdoc:
  swagger-ui:
    path: /swagger-ui.html
server:
  port: 8203
#  servlet:
#    context-path: /api/v1/oauth-server
security:
  oauth2:
    resource:
      jwt:
        key-value: JWTKey@1234
        key-uri: http://*************:8203/oauth/token_key
      id: 'carInventory1'
    client:
      client-id: appclient
      client-secret: appclient@123
purge.cron.expression: 0 0 5 * * ?

oauth-service:
  url: http://*************:8203
  refresh: http://*************:8203/oauth/token


#Eureka Client Configurations
eureka:         #tells about the Eureka server details and its refresh time
  instance:
    leaseRenewalIntervalInSeconds: 1
    leaseExpirationDurationInSeconds: 2
    prefer-ip-address: true
  client:
    serviceUrl:
      defaultZone: http://*************:8761/eureka/
    healthcheck:
      enabled: true
    lease:
      duration: 5

management:
  security:
    enabled: false  #disable the spring security on the management endpoints like /env, /refresh etc.


env:
  companyName: MyKarLink
  email: <EMAIL>
  supportEmail: <EMAIL>
  website: https://mykarlink.com
  mailPassword: GK28~N!$80p>
  paynowIntegrationId: 19634
  paynowIntegrationKey: 43f39671-fbc8-4d22-8453-a358a3b63d63
  paynowResultUrl: https://qa-api.myworklink.uk/worklink-api/api/v1/vehicle-booking/paynow-paid
  paynowReturnUrl: https://mykarlink.com/bookings/checkout/success
  companyLogo: https://admin.mykarlink.com/assets/images/svg/new-logo.svg
  systemCurrency: $

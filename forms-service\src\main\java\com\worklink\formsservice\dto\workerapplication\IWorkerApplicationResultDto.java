package com.worklink.formsservice.dto.workerapplication;

import java.time.Instant;
import java.time.LocalDate;

public interface IWorkerApplicationResultDto {

    Long getId();
    String getPosition();
    String getLocation();
    String getHours();
    LocalDate getClosingDate();
    String getTitle();
    String getLastname();
    String getFirstname();
    String getPreferredName();
    String getNationality();
    LocalDate getDob();
    String getAddress();
    String getEmail();
    String getPhone();
    Boolean getUkWork();
    String getRestrictionRes();
    String getInsuaranceNum();
    String getPersonalState();
    String getOffensesRes();
    String getInvestigationsRes();
    String getDbsCert();
    LocalDate getDbsRenewal();
    String getSigned();
    String getPrintName();
    LocalDate getSignDate();
    Boolean getLicence();
    Boolean getCar();
    Boolean getOnlyEmployment();
    String getSanctionsRes();
    String getDismissedRes();
    String getRelatedRes();
    String getGenFullname();
    String getGenSigned();
    LocalDate getGenDate();
    String getUnavailable();
    Boolean getSubmitted();

    Boolean getRestriction();
    Boolean getOffenses();
    Boolean getInvestigations();
    Boolean getSanctions();
    Boolean getDismissed();
    Boolean getRelated();
    Boolean getProfreg();
    Boolean getCertificate();
    Long getWorkerId();
    Instant getLastModifiedDate();
}

<div class="container-fluid">
  <form [formGroup]="currencyForm" (ngSubmit)="updateBaseCurrency()" class="example-form">
    <div class="card text-left">
      <div class="card-body">
        <h5 class="card-title">Currency Settings</h5>
        <p class="text-muted">Manage your agency's base currency for vehicle pricing and invoicing.</p>
        
        <div class="mt-4" *ngIf="!loading">
          <div class="row">
            <div class="col-md-6">
              <h6>Current Base Currency</h6>
              <div class="alert alert-info">
                <strong>{{ getCurrentCurrencyInfo().code }}</strong> - {{ getCurrentCurrencyInfo().name }}
                <span class="badge badge-secondary ml-2">{{ getCurrentCurrencyInfo().symbol }}</span>
              </div>
            </div>
          </div>

          <div class="mt-4">
            <h6>Select New Base Currency</h6>
            <p class="text-muted small">
              <i class="fas fa-info-circle"></i>
              Changing your base currency will affect how vehicle prices are displayed and how invoices are generated.
              Existing bookings and invoices will not be affected.
            </p>
            
            <!-- Quick Currency Selection -->
            <div class="mb-3">
              <label class="form-label">Common Currencies</label>
              <div class="d-flex flex-wrap gap-2">
                <button 
                  type="button" 
                  class="btn btn-sm"
                  [class.btn-primary]="currencyForm.value.baseCurrency === currency.code"
                  [class.btn-outline-primary]="currencyForm.value.baseCurrency !== currency.code"
                  *ngFor="let currency of commonCurrencies"
                  (click)="selectCurrency(currency.code)">
                  {{ currency.code }} ({{ currency.symbol }})
                </button>
              </div>
            </div>

            <!-- Dropdown Selection -->
            <div class="mb-3">
              <label for="baseCurrency" class="form-label">Or select from dropdown</label>
              <select 
                class="form-control" 
                id="baseCurrency"
                formControlName="baseCurrency">
                <option value="" disabled>Select a currency</option>
                <option 
                  *ngFor="let currency of commonCurrencies" 
                  [value]="currency.code">
                  {{ currency.code }} - {{ currency.name }} ({{ currency.symbol }})
                </option>
              </select>
            </div>

            <div class="mt-4">
              <button 
                type="submit" 
                class="btn btn-primary"
                [disabled]="!currencyForm.valid || updating">
                <span *ngIf="updating" class="spinner-border spinner-border-sm me-2"></span>
                {{ updating ? 'Updating...' : 'Update Base Currency' }}
              </button>
            </div>
          </div>

          <!-- Warning Section -->
          <div class="mt-4">
            <div class="alert alert-warning">
              <h6><i class="fas fa-exclamation-triangle"></i> Important Notes</h6>
              <ul class="mb-0">
                <li>Your base currency is used for setting vehicle daily rates and generating invoices</li>
                <li>Hirers will see prices converted to their preferred currency when browsing vehicles</li>
                <li>Exchange rates are automatically updated from Stripe</li>
                <li>Existing bookings and completed transactions will not be affected by this change</li>
              </ul>
            </div>
          </div>
        </div>

        <div *ngIf="loading" class="text-center py-4">
          <div class="spinner-border" role="status">
            <span class="sr-only">Loading...</span>
          </div>
          <p class="mt-2">Loading currency settings...</p>
        </div>
      </div>
    </div>
  </form>
</div>

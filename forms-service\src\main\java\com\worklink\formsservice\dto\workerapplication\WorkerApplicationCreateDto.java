package com.worklink.formsservice.dto.workerapplication;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.worklink.formsservice.dto.education.EducationCreateDto;
import com.worklink.formsservice.dto.education.IEducationResultDto;
import com.worklink.formsservice.dto.employer.EmployerCreateDto;
import com.worklink.formsservice.dto.employer.IEmployerResultDto;
import com.worklink.formsservice.dto.employment.EmploymentCreateDto;
import com.worklink.formsservice.dto.employment.IEmploymentResultDto;
import com.worklink.formsservice.dto.reference.IReferenceResultDto;
import com.worklink.formsservice.dto.reference.ReferenceCreateDto;
import com.worklink.formsservice.dto.reference.ReferencesCreateDto;
import com.worklink.formsservice.model.Education;
import com.worklink.formsservice.model.Employer;
import com.worklink.formsservice.model.Employment;
import com.worklink.formsservice.model.Reference;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class WorkerApplicationCreateDto {
    private String position;
    private String location;
    private String hours;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate closingDate;
    private String title;
    private String lastname;
    private String firstname;
    private String preferredName;
    private String nationality;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate dob;
    private String address;
    private String email;
    private String phone;
    private Boolean ukWork;
    private String restrictionRes;
    private String insuaranceNum;
    private String personalState;
    private String offensesRes;
    private String investigationsRes;
    private Boolean dbsCert;
    private Boolean dbs;
    private Boolean confidential;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate dbsRenewal;
    private String signed;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private String printName;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate signDate;
    private Boolean licence;
    private Boolean car;
    private Boolean onlyEmployment;
    private String sanctionsRes;
    private String dismissedRes;
    private String relatedRes;
    private String genFullname;
    private Boolean genSigned;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate genDate;
    private String unavailable;
    private Boolean submitted;
    private Long workerId;
    private Long agencyId;
    private String comment;
    private String status;

    private Boolean restriction;
    private Boolean offenses;
    private Boolean investigations;
    private Boolean availability;
    private Boolean sanctions;
    private Boolean dismissed;
    private Boolean related;


    private List<Reference> references;
    private List<Employer> employers;
    private List<Employment> employments;
    private List<Education> educations;

    private String DbsNumber;
    private String DbsType;
    private String lastModifiedDate;

    private String profreg;
    private String certificate;
}

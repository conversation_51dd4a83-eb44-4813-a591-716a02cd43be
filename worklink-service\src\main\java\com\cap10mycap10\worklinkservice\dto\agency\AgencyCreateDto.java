package com.cap10mycap10.worklinkservice.dto.agency;


import com.cap10mycap10.worklinkservice.dto.client.ClientDto;
import com.cap10mycap10.worklinkservice.enums.AgencyType;
import com.cap10mycap10.worklinkservice.enums.Status;
import com.cap10mycap10.worklinkservice.feigndtos.AdministratorCreateDto;
import com.cap10mycap10.worklinkservice.model.Address;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.Size;


@Data
public class AgencyCreateDto {

    @Size(min = 2)
    private String name;

    private String telephone;

    @Email
    private String email;

    private Address address;

    private String logo;


    private Status status;

    private AdministratorCreateDto administratorCreateDto;

    private Long serviceId;
    private AgencyType agencyType;

    private String billingEmail;

    private String baseCurrency = "USD"; // Default to USD

    public ClientDto toClientDto(){
        ClientDto clientDto = new ClientDto();
        clientDto.setName(name);
        clientDto.setEmail(email);
        clientDto.setTelephone(telephone);
        clientDto.setAddress(address);
        clientDto.setLogo(logo);
        return clientDto;
    }
}

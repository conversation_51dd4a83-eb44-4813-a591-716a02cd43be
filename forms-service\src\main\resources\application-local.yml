
spring:
  sleuth:
    sampler:
      probability: 1
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: *****************************************************************************************************************************************************************************************************************************
    username: 'root'
    password: 'password'
    driver-class-name: org.mariadb.jdbc.Driver
    hikari:
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
        useLocalSessionState: true
        rewriteBatchedStatements: true
        cacheResultSetMetadata: true
        cacheServerConfiguration: true
        elideSetAutoCommits: true
        maintainTimeStats: false
      maximum-pool-size: 10
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
  jpa:
    database-platform: org.hibernate.dialect.MariaDB103Dialect
    database: MYSQL
    hibernate:
      ddl-auto: update


    properties:
      hibernate.search.default.directory_provider: filesystem
      hibernate.search.default.indexBase: indexpath
      hibernate.format_sql: true
      hibernate.use_sql_comments: true
      hibernate.id.new_generator_mappings: true
      hibernate.connection.provider_disables_autocommit: true
      hibernate.cache.use_second_level_cache: false
      hibernate.cache.use_query_cache: false
      hibernate.generate_statistics: true
      hibernate.jdbc.batch_size: 500
      hibernate.order_inserts: true
      hibernate.order_updates: true
      hibernate.query.fail_on_pagination_over_collection_fetch: true
      hibernate.query.in_clause_parameter_padding: true
  data:
    jpa:
      repositories:
        bootstrap-mode: lazy

  jackson:
    serialization:
      fail-on-empty-beans: false
#springdoc:
#  swagger-ui:
#    path: /swagger-ui.html
server:
  port: 8307


gcp:
  config:
    file: gcp-account-file.json
  project:
    id: worklink
  bucket:
    id: worklink
  dir:
    name: worklink

logging.level.org.hibernate: ERROR
logging.level.org.hibernate.type.descriptor.sql: error




logging.logstash:
  enabled: true
  url: **************:5601

security:
  oauth2:
    resource:
      jwt:
        key-value: JWTKey@1234
        key-uri: http://localhost:8202/oauth/token_key
      id: 'carInventory'
    client:
      client-id: appclient
      client-secret: appclient@123
#Eureka Client Configurations
eureka:         #tells about the Eureka server details and its refresh time
  instance:
    leaseRenewalIntervalInSeconds: 1
    leaseExpirationDurationInSeconds: 2
    prefer-ip-address: true
  client:
    serviceUrl:
      defaultZone: http://127.0.0.1:8761/eureka/
    healthcheck:
      enabled: true
    lease:
      duration: 5

management:
  security:
    enabled: false  #disable the spring security on the management endpoints like /env, /refresh etc.


user.url: http://localhost:8202

api:
  url: https://qa-api.myworklink.uk/worklink-api/api/v1

iosApp:
  url: https://app.myworklink.uk/

androidApp:
  url: https://play.google.com/store/apps/details?id=uk.co.myworklink.myworklink

storage:
  volume:
    path: C:\\Users\\<USER>\\Videos


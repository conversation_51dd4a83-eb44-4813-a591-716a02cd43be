package com.cap10mycap10.worklinkservice.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

/**
 * Entity representing a currency in the system
 */
@Entity
@Table(name = "currency")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Currency {

    @Id
    @Column(length = 3)
    private String code; // ISO 4217 currency code (e.g., USD, GBP, EUR)

    @Column(nullable = false, length = 100)
    private String name; // Full currency name (e.g., US Dollar, British Pound)

    @Column(length = 5)
    private String symbol; // Currency symbol (e.g., $, £, €)

    @Column(nullable = false)
    private Boolean active = true; // Whether this currency is active in the system

    @Column(name = "decimal_places", nullable = false)
    private Integer decimalPlaces = 2; // Number of decimal places for this currency

    public Currency(String code, String name, String symbol) {
        this.code = code;
        this.name = name;
        this.symbol = symbol;
        this.active = true;
        this.decimalPlaces = 2;
    }
}

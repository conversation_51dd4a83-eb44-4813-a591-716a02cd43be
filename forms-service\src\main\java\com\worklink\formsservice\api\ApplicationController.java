package com.worklink.formsservice.api;


import com.worklink.formsservice.dto.education.EducationsCreateDto;
import com.worklink.formsservice.dto.education.IEducationResultDto;
import com.worklink.formsservice.dto.employer.EmployersCreateDto;
import com.worklink.formsservice.dto.employer.IEmployerResultDto;
import com.worklink.formsservice.dto.employment.EmploymentsCreateDto;
import com.worklink.formsservice.dto.employment.IEmploymentResultDto;
import com.worklink.formsservice.dto.reference.IReferenceResultDto;
import com.worklink.formsservice.dto.reference.ReferencesCreateDto;
import com.worklink.formsservice.dto.workerapplication.WorkerApplicationCreateDto;
import com.worklink.formsservice.dto.workerapplication.WorkerApplicationResultDto;
import com.worklink.formsservice.dto.workerapplication.WorkerApplicationUpdateDto;
import com.worklink.formsservice.model.Occupational;
import com.worklink.formsservice.model.WorkerApplication;
import com.worklink.formsservice.service.WorkerApplicationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.net.URI;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class ApplicationController {


    private final WorkerApplicationService workerApplicationService;

    public ApplicationController(WorkerApplicationService workerApplicationService) {
        this.workerApplicationService = workerApplicationService;
    }

    @PostMapping(value = "worker-application", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<WorkerApplicationResultDto> createWorkerApplication(@RequestBody WorkerApplicationCreateDto workerApplicationCreateDto) {
        log.info("Request to add workerApplication with : {}", workerApplicationCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.ok( workerApplicationService.addWorkerApplication(workerApplicationCreateDto));
    }


    @GetMapping(value = "worker-application/{workerId}")
    public ResponseEntity<WorkerApplicationResultDto> findWorkerApplications(
            @PathVariable("workerId") Long workerId){
        log.info("Request to get worker  workerApplication for worker_id: {}", workerId);
        return ResponseEntity.ok(workerApplicationService.findWorkerApplications(workerId));
    }


    @GetMapping(value = "worker-application/{workerId}/{agencyId}")
    public ResponseEntity<WorkerApplicationResultDto> findAgencyWorkerApplications
            (
                    @PathVariable("workerId") Long workerId,
                    @PathVariable("agencyId") Long agencyId
            ){
        log.info("Request to get worker  workerTraining for worker_id: {}", workerId);
        return ResponseEntity.ok(workerApplicationService.findAgencyWorkerApplication(workerId,agencyId));
    }


    @DeleteMapping(value = "worker-application/{id}")
    public ResponseEntity deleteWorkerApplication(@PathVariable("id") Long id) {
        workerApplicationService.deleteWorkerApplication(id);
        return ResponseEntity.noContent().build();
    }

    @PostMapping(value = "worker-application/safeguarding-upload",consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity uploadSafeguarding(@RequestParam("file") MultipartFile file,
                                             @RequestParam("workerId") Long workerId
    ) {

        log.info("Request to add worker profile image : {}");
        workerApplicationService.addSafeguardingSignature( workerId, file);
        return  ResponseEntity.noContent().build();
    }



    @PostMapping(value = "worker-application/general-upload",consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity uploadGeneralSignature(@RequestParam("file") MultipartFile file,
                                             @RequestParam("workerId") Long workerId
    ){

        log.info("Request to add worker profile image : {}");
        workerApplicationService.addGeneralSignature( workerId, file);
        return  ResponseEntity.noContent().build();
    }


    @PutMapping(value = "worker-application", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Occupational> reviewApplication(@RequestBody WorkerApplicationCreateDto occupational) {
        log.info("Request to update workerTraining with : {}", occupational);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        workerApplicationService.updateWorkerApplication(occupational);
        return ResponseEntity.created(uri)
                .build();
    }



//    @GetMapping(value = "worker-compliance/{workerId}/{agencyId}/{page}/{size}")
//    public ResponseEntity<List<WorkerApplicationResultDto>> findAgencyWorkerCompliances
//            (
//                    @PathVariable("workerId") Long workerId,
//                    @PathVariable("agencyId") Long agencyId,
//                    @PathVariable("page") int page,
//                    @PathVariable("size") int size
//            ){
//        log.info("Request to get worker  workerTraining for worker_id: {}", workerId);
//        return ResponseEntity.ok(workerApplicationService.findAgencyWorkerApplications(workerId,agencyId, PageRequest.of(page, size)));
//    }
}

package com.worklink.formsservice.dao;


import com.worklink.formsservice.dto.employer.IEmployerResultDto;
import com.worklink.formsservice.model.Employer;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface EmployerRepository extends JpaRepository<Employer, Long> {

    List<IEmployerResultDto> findAllByWorkerApplicationId(Long workerApplicationId);
}

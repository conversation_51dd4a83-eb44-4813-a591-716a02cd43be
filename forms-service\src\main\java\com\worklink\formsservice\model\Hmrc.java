package com.worklink.formsservice.model;

import lombok.AllArgsConstructor;
import lombok.Data;

import javax.persistence.*;
import java.time.LocalDate;


@Entity
@AllArgsConstructor
@Data
public class Hmrc extends AbstractAuditingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String lastname;
    private String firstname;
    private String address;
    private String insuarance;
    private LocalDate employmentStart;
    private String sex;
    private String dob;

    private Boolean otherJob;
    private Boolean payments;
    private Boolean april;


    private String statement;

    private Boolean loan;

    private Boolean applyStatement;
    private String plan;

    private String signed;
    private LocalDate signDate;
    private String fullname;
    private String submitted;

    private String agencyId;
    private String comment;
    private String status;

    @Column(unique = true, nullable = false)
    private Long workerId;

//    @OneToOne(fetch = FetchType.LAZY)
//    private Worker worker;

    public Hmrc() {};


}

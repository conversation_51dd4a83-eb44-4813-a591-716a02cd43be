package com.worklink.formsservice.dao;


import com.worklink.formsservice.dto.workerapplication.IWorkerApplicationResultDto;
import com.worklink.formsservice.dto.workerapplication.WorkerApplicationResultDto;
import com.worklink.formsservice.model.WorkerApplication;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface WorkerApplicationRepository extends JpaRepository<WorkerApplication, Long> {

    @Query(value = "select *\n" +
            "from worker_application\n" +
            "where worker_id = ?1" , nativeQuery = true)
    Page<IWorkerApplicationResultDto> findAllWorkerApplications(Long workerId, PageRequest of);

    @Query(value = "select id,\n" +
            "position,\n" +
            "location,\n" +
            "hours,\n" +
            "closing_date as closingDate,\n" +
            "title,\n" +
            "lastname,\n" +
            "firstname,\n" +
            "preferred_name as preferredName,\n" +
            "nationality,\n" +
            "dob,\n" +
            "address,\n" +
            "email,\n" +
            "phone,\n" +
            "uk_work as ukWork,\n" +
            "restriction_res as restrictionRes,\n" +
            "insuarance_num as insuaranceNum,\n" +
            "personal_state as personalState,\n" +
            "offenses_res as offensesRes,\n" +
            "investigations_res as investigationsRes,\n" +
            "dbs_cert as dbsCert,\n" +
            "dbs_renewal as dbsRenewal,\n" +
            "signed,\n" +
            "print_name as printName,\n" +
            "sign_date as signDate,\n" +
            "licence,\n" +
            "car,\n" +
            "only_employment as onlyEmployment,\n" +
            "sanctions_res as sanctionsRes,\n" +
            "dismissed_res as dismissedRes,\n" +
            "related_res as relatedRes,\n" +
            "gen_fullname as genFullname,\n" +
            "gen_signed as genSigned,\n" +
            "gen_date as genDate,\n" +
            "unavailable,\n" +
            "submitted,\n" +
            "restriction,\n" +
            "offenses,\n" +
            "availability,\n" +
            "investigations,\n" +
            "sanctions,\n" +
            "dismissed,\n" +
            " created_date as createdDate, " +
            "created_by as createdBy," +
            "version, " +
            " last_modified_by as lastModifiedBy," +
            "last_modified_date as lastModifiedDate," +
            "related," +
            "dbs," +
            "dbs_number as dbsNumber, \n"+
            "dbs_type as dbsType, \n"+
            "confidential," +
            "worker_id as workerId\n" +
            "from worker_application\n" +
            "where worker_id = ?1" , nativeQuery = true)
    IWorkerApplicationResultDto findWorkerApplication(Long workerId);

    WorkerApplication findByWorkerId(Long id);



    @Query(value = "select *     \n" +
            "from worker_application\n" +
            "where worker_id = ?1" , nativeQuery = true)
    Page<WorkerApplication> findAllWorkerApplications2(Long workerId, PageRequest of);


    @Query(value = "select * from worker_application where id = ?1 order by datediff(start,CURDATE()) asc", nativeQuery = true)
    WorkerApplicationResultDto findByIdn(Long id);
}

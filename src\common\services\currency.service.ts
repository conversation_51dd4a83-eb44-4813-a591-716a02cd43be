import { Currency, ExchangeRate } from "@/common/models";
import { apiClient } from "@/common/lib/api-client";

/**
 * Service for currency-related API operations
 */
export class CurrencyService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL + "/karlink-service/api/v1";
  }

  /**
   * Get all active currencies
   */
  async getActiveCurrencies(): Promise<Currency[]> {
    const response = await apiClient.get<Currency[]>(`${this.baseUrl}/currencies/active`);
    return response.data;
  }

  /**
   * Get all currencies (active and inactive)
   */
  async getAllCurrencies(): Promise<Currency[]> {
    const response = await apiClient.get<Currency[]>(`${this.baseUrl}/currencies`);
    return response.data;
  }

  /**
   * Get a specific currency by code
   */
  async getCurrency(code: string): Promise<Currency> {
    const response = await apiClient.get<Currency>(`${this.baseUrl}/currencies/${code}`);
    return response.data;
  }

  /**
   * Get exchange rate between two currencies
   */
  async getExchangeRate(fromCurrency: string, toCurrency: string): Promise<ExchangeRate> {
    const response = await apiClient.get<ExchangeRate>(
      `${this.baseUrl}/exchange-rates/${fromCurrency}/${toCurrency}`
    );
    return response.data;
  }

  /**
   * Get multiple exchange rates for a base currency
   */
  async getExchangeRates(baseCurrency: string, targetCurrencies: string[]): Promise<ExchangeRate[]> {
    const response = await apiClient.post<ExchangeRate[]>(
      `${this.baseUrl}/exchange-rates/batch`,
      {
        baseCurrency,
        targetCurrencies,
      }
    );
    return response.data;
  }

  /**
   * Convert amount between currencies
   */
  async convertCurrency(
    amount: number,
    fromCurrency: string,
    toCurrency: string
  ): Promise<{
    originalAmount: number;
    convertedAmount: number;
    exchangeRate: number;
    fromCurrency: string;
    toCurrency: string;
  }> {
    const response = await apiClient.post(
      `${this.baseUrl}/exchange-rates/convert`,
      {
        amount,
        fromCurrency,
        toCurrency,
      }
    );
    return response.data;
  }

  /**
   * Update user's preferred currency
   */
  async updateUserPreferredCurrency(userId: number, currencyCode: string): Promise<void> {
    await apiClient.put(`${this.baseUrl}/users/${userId}/preferred-currency`, {
      preferredCurrency: currencyCode,
    });
  }

  /**
   * Update agency's base currency
   */
  async updateAgencyBaseCurrency(agencyId: number, currencyCode: string): Promise<void> {
    await apiClient.put(`${this.baseUrl}/agencies/${agencyId}/base-currency`, {
      baseCurrency: currencyCode,
    });
  }

  /**
   * Refresh exchange rates from Stripe
   */
  async refreshExchangeRates(): Promise<void> {
    await apiClient.post(`${this.baseUrl}/exchange-rates/refresh`);
  }

  /**
   * Get historical exchange rates
   */
  async getHistoricalRates(
    fromCurrency: string,
    toCurrency: string,
    fromDate: string,
    toDate: string
  ): Promise<ExchangeRate[]> {
    const response = await apiClient.get<ExchangeRate[]>(
      `${this.baseUrl}/exchange-rates/historical`,
      {
        params: {
          fromCurrency,
          toCurrency,
          fromDate,
          toDate,
        },
      }
    );
    return response.data;
  }
}

// Export a singleton instance
export const currencyService = new CurrencyService();

package com.worklink.formsservice.dao;

import com.worklink.formsservice.model.AgencyWorkerApplication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface AgencyWorkerApplicationRepository extends JpaRepository<AgencyWorkerApplication, Long> {

    AgencyWorkerApplication findByWorkerIdAndAgencyId(Long workerId, Long agencyId);


}
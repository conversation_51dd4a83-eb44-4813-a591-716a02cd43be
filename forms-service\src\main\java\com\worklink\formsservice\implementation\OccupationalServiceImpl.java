package com.worklink.formsservice.implementation;

import com.worklink.formsservice.dao.*;
import com.worklink.formsservice.dto.file.FileDto;
import com.worklink.formsservice.dto.occupational.OccupationalCreateDto;
import com.worklink.formsservice.dto.occupational.OccupationalResultDto;
import com.worklink.formsservice.dto.workerapplication.WorkerApplicationResultDto;
import com.worklink.formsservice.enums.FormStatus;
import com.worklink.formsservice.exception.BusinessValidationException;
import com.worklink.formsservice.exception.RecordNotFoundException;
import com.worklink.formsservice.helpers.DataBucketUtil;
import com.worklink.formsservice.model.*;
import com.worklink.formsservice.service.OccupationalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static java.time.LocalDateTime.now;

@Service
@Slf4j
public class OccupationalServiceImpl implements OccupationalService {
    @Value("${storage.volume.path}")
    private  String rootPath;


//    private final OccupationalToOccupationalResultDto toOccupationalResultDto;

    private final OccupationalRepository occupationalRepository;

    private final EmployerRepository employerRepository;
    private final AgencyWorkerOccupationalRepository agencyWorkerOccupationalRepository;
    private final EducationRepository educationRepository;
    private final ReferenceRepository referenceRepository;
    private final EmploymentRepository employmentRepository;
    private final DataBucketUtil dataBucketUtil;

    public OccupationalServiceImpl(OccupationalRepository occupationalRepository, EmployerRepository employerRepository, AgencyWorkerOccupationalRepository agencyWorkerOccupationalRepository, EducationRepository educationRepository, ReferenceRepository referenceRepository, EmploymentRepository employmentRepository, DataBucketUtil dataBucketUtil) {
        this.occupationalRepository = occupationalRepository;
        this.employerRepository = employerRepository;
        this.agencyWorkerOccupationalRepository = agencyWorkerOccupationalRepository;
        this.educationRepository = educationRepository;
        this.referenceRepository = referenceRepository;
        this.employmentRepository = employmentRepository;
        this.dataBucketUtil = dataBucketUtil;
    }


    @Override
    public OccupationalCreateDto addOccupational(OccupationalCreateDto occupationalCreateDto) {
        Occupational occupational = new Occupational();
        Occupational occupational1 = occupationalRepository.findByWorkerId(occupationalCreateDto.getWorkerId());

        if(occupational1!=null) {
            occupational =occupational1;
        }
        occupational.setTitle(occupationalCreateDto.getTitle());
        occupational.setLastname(occupationalCreateDto.getLastname());
        occupational.setFirstname(occupationalCreateDto.getFirstname());
        occupational.setPreferredName(occupationalCreateDto.getPreferredName());
        occupational.setNationality(occupationalCreateDto.getNationality());
        occupational.setDob(occupationalCreateDto.getDob());
        occupational.setAddress(occupationalCreateDto.getAddress());
        occupational.setEmail(occupationalCreateDto.getEmail());
        occupational.setPhone(occupationalCreateDto.getPhone());
        occupational.setDisability(occupationalCreateDto.getDisability());
        occupational.setDisabilityRes(occupationalCreateDto.getDisabilityRes());
        occupational.setDisabilityByWork(occupationalCreateDto.getDisabilityByWork());
        occupational.setDisabilityByWorkRes(occupationalCreateDto.getDisabilityByWorkRes());
        occupational.setCurrentTreatment(occupationalCreateDto.getCurrentTreatment());
        occupational.setCurrentTreatmentRes(occupationalCreateDto.getCurrentTreatmentRes());
        occupational.setAssistance(occupationalCreateDto.getAssistance());
        occupational.setAssistanceRes(occupationalCreateDto.getAssistanceRes());
        occupational.setMrsa(occupationalCreateDto.getMrsa());
        occupational.setMrsaDate(occupationalCreateDto.getMrsaDate());
        occupational.setCdiff(occupationalCreateDto.getCdiff());
        occupational.setCdiffDate(occupationalCreateDto.getCdiffDate());
        occupational.setChickenpox(occupationalCreateDto.getChickenpox());
        occupational.setChickenpoxDate(occupationalCreateDto.getChickenpoxDate());
        occupational.setBbv(occupationalCreateDto.getBbv());
        occupational.setBbvDate(occupationalCreateDto.getBbvDate());
        occupational.setTravelled(occupationalCreateDto.getTravelled());
        occupational.setBcg(occupationalCreateDto.getBcg());
        occupational.setBcgDate(occupationalCreateDto.getBcgDate());
        occupational.setCough(occupationalCreateDto.getCough());
        occupational.setWeight(occupationalCreateDto.getWeight());
        occupational.setFever(occupationalCreateDto.getFever());
        occupational.setTb(occupationalCreateDto.getTb());
        occupational.setTbcRes(occupationalCreateDto.getTbcRes());
        occupational.setTripleVac(occupationalCreateDto.getTripleVac());
        occupational.setTripleVacDate(occupationalCreateDto.getTripleVacDate());
        occupational.setPolio(occupationalCreateDto.getPolio());
        occupational.setPolioDate(occupationalCreateDto.getPolioDate());
        occupational.setTetanus(occupationalCreateDto.getTetanus());
        occupational.setTetanusDate(occupationalCreateDto.getTetanusDate());
        occupational.setHepb(occupationalCreateDto.getHepb());
        occupational.setC1(occupationalCreateDto.getC1());
        occupational.setC2(occupationalCreateDto.getC2());
        occupational.setC3(occupationalCreateDto.getC3());
        occupational.setB1(occupationalCreateDto.getB1());
        occupational.setB2(occupationalCreateDto.getB2());
        occupational.setB3(occupationalCreateDto.getB3());

        occupational.setExposure(occupationalCreateDto.getExposure());
        occupational.setConsent(occupationalCreateDto.getConsent());
        occupational.setCopy(occupationalCreateDto.getCopy());
        occupational.setFullname(occupationalCreateDto.getFullname());
        occupational.setSigDate(occupationalCreateDto.getSigDate());
        occupational.setWorkerId(occupationalCreateDto.getWorkerId());
        occupational.setLastModifiedDate(now());

        occupationalRepository.saveAndFlush(occupational);

        return occupationalCreateDto;
    }



    @Override
    public void updateWorkerOccupational(OccupationalCreateDto application){

        AgencyWorkerOccupational agencyWorkerApplication;

        agencyWorkerApplication = agencyWorkerOccupationalRepository
                .findByWorkerIdAndAgencyId(
                        application.getWorkerId(),
                        application.getAgencyId()
                );

        if (agencyWorkerApplication   ==  null){

            agencyWorkerApplication = new AgencyWorkerOccupational();
            agencyWorkerApplication.setAgencyId(application.getAgencyId());
            agencyWorkerApplication.setWorkerId(application.getWorkerId());

        }


        log.info("Updated worker-application{}", agencyWorkerApplication);


        agencyWorkerApplication.setComment(application.getComment());

        if (application.getStatus().equalsIgnoreCase(FormStatus.REJECTED.toString())){
            agencyWorkerApplication.setStatus(FormStatus.REJECTED);
        }

        if (application.getStatus().equalsIgnoreCase(FormStatus.APPROVED.toString())){
            agencyWorkerApplication.setStatus(FormStatus.APPROVED);
        }

        agencyWorkerOccupationalRepository.save(agencyWorkerApplication);




    }



    @Override
    public void addApplicationDoc(Long workerId,Long compliance, MultipartFile file) {
        log.info("Request to upload a payslip");
        String name = "occupational-"+compliance+".pdf";

        Occupational occupational = getOne(compliance);


        List<String> types = new ArrayList<String>();

        types.add("application/pdf");


        if (!types.contains(file.getContentType())) {
            throw new BusinessValidationException("Uploaded file type is not supported. Please upload a pdf");
        }

        if (!file.isEmpty() ) {
            try {
                byte[] bytes = file.getBytes();

                File dir = new File(rootPath + File.separator + "worker"+ File.separator + workerId  );

                if (!dir.exists())  dir.mkdirs();

                // Create the file on server
                File serverFile = new File(dir.getAbsolutePath()
                        + File.separator + name);
                BufferedOutputStream stream = new BufferedOutputStream(
                        new FileOutputStream(serverFile));
                stream.write(bytes);
                stream.close();


                log.info("Server File Location="
                        + serverFile.getAbsolutePath());

                log.info("Uploaded");
//                occupational.setUploaded(true);
                occupationalRepository.save(occupational);

            } catch (Exception e) {
                log.info("You failed to upload");
                log.error(e.getMessage());
                throw new BusinessValidationException("Upload failed.");

            }

        } else {
            log.info("You failed to upload");
            throw new BusinessValidationException("Uploaded file is not empty.");

        }
    }



    @Override
    public void deleteOccupational(Long id) {

        Occupational availability = occupationalRepository.findById(id).get();

        occupationalRepository.delete(availability);
        occupationalRepository.flush();


    }

    @Override
    public Occupational findById(Long id) {
        return null;
    }

    @Override
    public OccupationalResultDto findOccupationals(Long workerId) {

        Occupational occupational = occupationalRepository.findByWorkerId(workerId);
        DateTimeFormatter pattern = DateTimeFormatter.ofPattern("dd/MM/yyyy");
//        log.info("Found worker occupational: {}", occupational.getLastModifiedDate());
        if(occupational==null){
            return null;
        }
        if(occupational.getLastModifiedDate()!=null)occupational.setLastModifiedBy(occupational.getLastModifiedDate().format(pattern));

        return convert(occupational);
    }

    private OccupationalResultDto convert(Occupational occupational) {

        OccupationalResultDto occupationalResultDto = new OccupationalResultDto();


        occupationalResultDto.setId(occupational.getId());
        occupationalResultDto.setTitle(occupational.getTitle());
        occupationalResultDto.setLastname(occupational.getLastname());
        occupationalResultDto.setFirstname(occupational.getFirstname());
        occupationalResultDto.setPreferredName(occupational.getPreferredName());
        occupationalResultDto.setNationality(occupational.getNationality());
        occupationalResultDto.setDob(occupational.getDob());
        occupationalResultDto.setAddress(occupational.getAddress());
        occupationalResultDto.setEmail(occupational.getEmail());
        occupationalResultDto.setPhone(occupational.getPhone());
        occupationalResultDto.setDisability(occupational.getDisability());
        occupationalResultDto.setDisabilityRes(occupational.getDisabilityRes());
        occupationalResultDto.setDisabilityByWork(occupational.getDisabilityByWork());
        occupationalResultDto.setDisabilityByWorkRes(occupational.getDisabilityByWorkRes());
        occupationalResultDto.setCurrentTreatment(occupational.getCurrentTreatment());
        occupationalResultDto.setCurrentTreatmentRes(occupational.getCurrentTreatmentRes());
        occupationalResultDto.setAssistance(occupational.getAssistance());
        occupationalResultDto.setAssistanceRes(occupational.getAssistanceRes());
        occupationalResultDto.setMrsa(occupational.getMrsa());
        occupationalResultDto.setMrsaDate(occupational.getMrsaDate());
        occupationalResultDto.setCdiff(occupational.getCdiff());
        occupationalResultDto.setCdiffDate(occupational.getCdiffDate());
        occupationalResultDto.setChickenpox(occupational.getChickenpox());
        occupationalResultDto.setChickenpoxDate(occupational.getChickenpoxDate());
        occupationalResultDto.setBbv(occupational.getBbv());
        occupationalResultDto.setBbvDate(occupational.getBbvDate());
        occupationalResultDto.setTravelled(occupational.getTravelled());
        occupationalResultDto.setBcg(occupational.getBcg());
        occupationalResultDto.setBcgDate(occupational.getBcgDate());
        occupationalResultDto.setCough(occupational.getCough());
        occupationalResultDto.setWeight(occupational.getWeight());
        occupationalResultDto.setFever(occupational.getFever());
        occupationalResultDto.setTb(occupational.getTb());
        occupationalResultDto.setTbcRes(occupational.getTbcRes());
        occupationalResultDto.setTripleVac(occupational.getTripleVac());
        occupationalResultDto.setTripleVacDate(occupational.getTripleVacDate());
        occupationalResultDto.setPolio(occupational.getPolio());
        occupationalResultDto.setPolioDate(occupational.getPolioDate());
        occupationalResultDto.setTetanus(occupational.getTetanus());
        occupationalResultDto.setTetanusDate(occupational.getTetanusDate());
        occupationalResultDto.setHepb(occupational.getHepb());
        occupationalResultDto.setC1(occupational.getC1());
        occupationalResultDto.setC2(occupational.getC2());
        occupationalResultDto.setC3(occupational.getC3());
        occupationalResultDto.setB1(occupational.getB1());
        occupationalResultDto.setB2(occupational.getB2());
        occupationalResultDto.setB3(occupational.getB3());
        occupationalResultDto.setVarUp(occupational.getVarUp());
        occupationalResultDto.setTbUp(occupational.getTbUp());
        occupationalResultDto.setRubUp(occupational.getRubUp());
        occupationalResultDto.setHepbUP(occupational.getHepbUP());
        occupationalResultDto.setHepbsUp(occupational.getHepbsUp());
        occupationalResultDto.setHepcUp(occupational.getHepcUp());
        occupationalResultDto.setHivUp(occupational.getHivUp());
        occupationalResultDto.setExposure(occupational.getExposure());
        occupationalResultDto.setConsent(occupational.getConsent());
        occupationalResultDto.setCopy(occupational.getCopy());
        occupationalResultDto.setFullname(occupational.getFullname());
        occupationalResultDto.setSigUp(occupational.getSigUp());
        occupationalResultDto.setSigDate(occupational.getSigDate());
        occupationalResultDto.setWorkerId(occupational.getWorkerId());
        occupationalResultDto.setLastModifiedDate(occupational.getLastModifiedBy());


        return occupationalResultDto;
    }

    @Override
    public OccupationalResultDto findAgencyWorkerOccupational(Long workerId, Long agencyId) {


        Occupational occupational = occupationalRepository.findByWorkerId(workerId);
        if(occupational==null){
            return null;
        }
        DateTimeFormatter pattern = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        log.info("Found worker occupational: {}", occupational.getLastModifiedDate());
        if(occupational.getLastModifiedDate()!=null)occupational.setLastModifiedBy(occupational.getLastModifiedDate().format(pattern));


        if(occupational==null){
            throw new RecordNotFoundException("Worker occupational not found");
        }


        AgencyWorkerOccupational app;

        app = agencyWorkerOccupationalRepository.findByWorkerIdAndAgencyId(workerId, agencyId);

        if(app== null) {
            app = new AgencyWorkerOccupational();
        }

        OccupationalResultDto occupationalres = convert(occupational);

        if(app.getStatus()!=null) occupationalres.setStatus(app.getStatus().toString());
        if(app.getAgencyId()!=null) occupationalres.setAgencyId(app.getAgencyId().toString());
        if(app.getComment()!=null) occupationalres.setComment(app.getComment());




        return occupationalres;
    }



    @Override
    public Page<Occupational> findAllPaged(PageRequest of) {
        return null;
    }

    @Override
    public Occupational findByWorkerId(Long id) {
        return occupationalRepository.findByWorkerId(id);
    }

    @Override
    public Occupational save(OccupationalCreateDto occupationalUpdateDto) {
        return null;
    }

    @Override
    public Occupational getOne(Long id) {
        return occupationalRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Worker occupational not found"));
    }





    @Override
    public void addHiv(Long workerId, MultipartFile files) {


        log.info("Start file uploading service");

        Arrays.asList(files).forEach(file -> {
            String originalFileName = file.getOriginalFilename();
            if(originalFileName == null){
                throw new BusinessValidationException("Original file city is null");
            }
            Path path = new File(originalFileName).toPath();

            String contentType = null;
            try {
                contentType = Files.probeContentType(path);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            FileDto fileDto = dataBucketUtil.uploadFile(file, "w/f/"+ workerId +"/"+originalFileName, contentType);

            if (fileDto != null) {
                Occupational occupational = occupationalRepository.findByWorkerId(workerId);
                if(occupational==null){
                    occupational = new Occupational();
                    occupational.setWorkerId(workerId);
                }



                occupational.setHivUp(fileDto.getFileUrl());

                occupationalRepository.save(occupational);
                log.debug("File uploaded successfully, file city: {} and url: {}",fileDto.getFileName(), fileDto.getFileUrl() );
            }
            log.debug("File details successfully saved in the database");

        });

    }

    @Override
    public void addHepc(Long workerId, MultipartFile files) {


        log.info("Start file uploading service");
//        List<Bank> inputFiles = new ArrayList<>();

        Arrays.asList(files).forEach(file -> {
            String originalFileName = file.getOriginalFilename();
            if(originalFileName == null){
                throw new BusinessValidationException("Original file city is null");
            }
            Path path = new File(originalFileName).toPath();

            String contentType = null;
            try {
                contentType = Files.probeContentType(path);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            FileDto fileDto = dataBucketUtil.uploadFile(file, "w/f/"+ workerId+"/" +originalFileName, contentType);

            if (fileDto != null) {
                Occupational occupational = occupationalRepository.findByWorkerId(workerId);
                if(occupational==null){
                    occupational = new Occupational();
                    occupational.setWorkerId(workerId);
                }



                occupational.setHepcUp(fileDto.getFileUrl());

                occupationalRepository.save(occupational);
                log.debug("File uploaded successfully, file city: {} and url: {}",fileDto.getFileName(), fileDto.getFileUrl() );
            }
            log.debug("File details successfully saved in the database");

        });
    }

    @Override
    public void addHepbs(Long workerId, MultipartFile files) {


        log.info("Start file uploading service");
//        List<Bank> inputFiles = new ArrayList<>();

        Arrays.asList(files).forEach(file -> {
            String originalFileName = file.getOriginalFilename();
            if(originalFileName == null){
                throw new BusinessValidationException("Original file city is null");
            }
            Path path = new File(originalFileName).toPath();

            String contentType = null;
            try {
                contentType = Files.probeContentType(path);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            FileDto fileDto = dataBucketUtil.uploadFile(file, "w/f/"+ workerId+"/" +originalFileName, contentType);

            if (fileDto != null) {
                Occupational occupational = occupationalRepository.findByWorkerId(workerId);
                if(occupational==null){
                    occupational = new Occupational();
                    occupational.setWorkerId(workerId);
                }



                occupational.setHepbsUp(fileDto.getFileUrl());

                occupationalRepository.save(occupational);
                log.debug("File uploaded successfully, file city: {} and url: {}",fileDto.getFileName(), fileDto.getFileUrl() );
            }
            log.debug("File details successfully saved in the database");

        });
    }

    @Override
    public void addHepb(Long workerId, MultipartFile files) {

        log.info("Start file uploading service");
//        List<Bank> inputFiles = new ArrayList<>();

        Arrays.asList(files).forEach(file -> {
            String originalFileName = file.getOriginalFilename();
            if(originalFileName == null){
                throw new BusinessValidationException("Original file city is null");
            }
            Path path = new File(originalFileName).toPath();

            String contentType = null;
            try {
                contentType = Files.probeContentType(path);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            FileDto fileDto = dataBucketUtil.uploadFile(file, "w/f/"+ workerId+"/" +originalFileName, contentType);

            if (fileDto != null) {
                Occupational occupational = occupationalRepository.findByWorkerId(workerId);
                occupational.setWorkerId(workerId);
                if(occupational==null){
                    occupational = new Occupational();
                }


                occupational.setHepbUP(fileDto.getFileUrl());

                occupationalRepository.save(occupational);
                log.debug("File uploaded successfully, file city: {} and url: {}",fileDto.getFileName(), fileDto.getFileUrl() );
            }
            log.debug("File details successfully saved in the database");

        });
    }

    @Override
    public void addMmr(Long workerId, MultipartFile files) {

        log.info("Start file uploading service");
//        List<Bank> inputFiles = new ArrayList<>();

        Arrays.asList(files).forEach(file -> {
            String originalFileName = file.getOriginalFilename();
            if(originalFileName == null){
                throw new BusinessValidationException("Original file city is null");
            }
            Path path = new File(originalFileName).toPath();

            String contentType = null;
            try {
                contentType = Files.probeContentType(path);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            FileDto fileDto = dataBucketUtil.uploadFile(file, "w/f/"+ workerId+"/" +originalFileName, contentType);

            if (fileDto != null) {
                Occupational occupational = occupationalRepository.findByWorkerId(workerId);
                if(occupational==null){
                    occupational = new Occupational();
                    occupational.setWorkerId(workerId);
                }



                occupational.setRubUp(fileDto.getFileUrl());

                occupationalRepository.save(occupational);
                log.debug("File uploaded successfully, file city: {} and url: {}",fileDto.getFileName(), fileDto.getFileUrl() );
            }
            log.debug("File details successfully saved in the database");

        });
    }

    @Override
    public void addTb(Long workerId, MultipartFile files) {

        log.info("Start file uploading service");
//        List<Bank> inputFiles = new ArrayList<>();

        Arrays.asList(files).forEach(file -> {
            String originalFileName = file.getOriginalFilename();
            if(originalFileName == null){
                throw new BusinessValidationException("Original file city is null");
            }
            Path path = new File(originalFileName).toPath();

            String contentType = null;
            try {
                contentType = Files.probeContentType(path);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            FileDto fileDto = dataBucketUtil.uploadFile(file, "w/f/"+ workerId+"/" +originalFileName, contentType);

            if (fileDto != null) {
                Occupational occupational = occupationalRepository.findByWorkerId(workerId);
                if(occupational==null){
                    occupational = new Occupational();
                    occupational.setWorkerId(workerId);
                }



                occupational.setTbUp(fileDto.getFileUrl());

                occupationalRepository.save(occupational);
                log.debug("File uploaded successfully, file city: {} and url: {}",fileDto.getFileName(), fileDto.getFileUrl() );
            }
            log.debug("File details successfully saved in the database");

        });

    }

    @Override
    public void addVaricella(Long workerId, MultipartFile files) {

        log.info("Start file uploading service");
//        List<Bank> inputFiles = new ArrayList<>();

        Arrays.asList(files).forEach(file -> {
            String originalFileName = file.getOriginalFilename();
            if(originalFileName == null){
                throw new BusinessValidationException("Original file city is null");
            }
            Path path = new File(originalFileName).toPath();

            String contentType = null;
            try {
                contentType = Files.probeContentType(path);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            FileDto fileDto = dataBucketUtil.uploadFile(file, "w/f/"+ workerId +"/"+originalFileName, contentType);

            if (fileDto != null) {
                Occupational occupational = occupationalRepository.findByWorkerId(workerId);
                if(occupational==null){
                    occupational = new Occupational();
                    occupational.setWorkerId(workerId);
                }



                occupational.setVarUp(fileDto.getFileUrl());

                occupationalRepository.save(occupational);
                log.debug("File uploaded successfully, file city: {} and url: {}",fileDto.getFileName(), fileDto.getFileUrl() );
            }
            log.debug("File details successfully saved in the database");

        });

    }

    @Override
    public void addSignature(Long workerId, MultipartFile files) {

        log.info("Start file uploading service");
//        List<Bank> inputFiles = new ArrayList<>();

        Arrays.asList(files).forEach(file -> {
            String originalFileName = file.getOriginalFilename();
            if(originalFileName == null){
                throw new BusinessValidationException("Original file city is null");
            }
            Path path = new File(originalFileName).toPath();

            String contentType = null;
            try {
                contentType = Files.probeContentType(path);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            FileDto fileDto = dataBucketUtil.uploadFile(file, "w/f/"+ workerId+"/" +originalFileName, contentType);

            if (fileDto != null) {
                Occupational occupational = occupationalRepository.findByWorkerId(workerId);
                if(occupational==null){
                    occupational = new Occupational();
                    occupational.setWorkerId(workerId);
                }



                occupational.setSigUp(fileDto.getFileUrl());

                occupationalRepository.save(occupational);
                log.debug("File uploaded successfully, file city: {} and url: {}",fileDto.getFileName(), fileDto.getFileUrl() );
            }
            log.debug("File details successfully saved in the database");

        });

    }


}

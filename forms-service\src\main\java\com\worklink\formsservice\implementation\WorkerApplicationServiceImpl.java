package com.worklink.formsservice.implementation;

import com.worklink.formsservice.dao.*;
import com.worklink.formsservice.dto.education.EducationsCreateDto;
import com.worklink.formsservice.dto.education.IEducationResultDto;
import com.worklink.formsservice.dto.employer.EmployersCreateDto;
import com.worklink.formsservice.dto.employer.IEmployerResultDto;
import com.worklink.formsservice.dto.employment.EmploymentsCreateDto;
import com.worklink.formsservice.dto.employment.IEmploymentResultDto;
import com.worklink.formsservice.dto.file.FileDto;
import com.worklink.formsservice.dto.reference.IReferenceResultDto;
import com.worklink.formsservice.dto.reference.ReferenceCreateDto;
import com.worklink.formsservice.dto.reference.ReferencesCreateDto;
import com.worklink.formsservice.dto.workerapplication.WorkerApplicationCreateDto;
import com.worklink.formsservice.dto.workerapplication.WorkerApplicationResultDto;
import com.worklink.formsservice.dto.workerapplication.WorkerApplicationUpdateDto;
import com.worklink.formsservice.enums.FormStatus;
import com.worklink.formsservice.exception.BusinessValidationException;
import com.worklink.formsservice.exception.RecordNotFoundException;
import com.worklink.formsservice.helpers.DataBucketUtil;
import com.worklink.formsservice.mapper.workerapplication.WorkerApplicationToWorkerApplicationResultDto;
import com.worklink.formsservice.model.*;

import com.worklink.formsservice.service.WorkerApplicationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.time.LocalDateTime.now;


@Service
@Slf4j
public class WorkerApplicationServiceImpl implements WorkerApplicationService {
    @Value("${storage.volume.path}")
    private  String rootPath;


    private final WorkerApplicationToWorkerApplicationResultDto toWorkerApplicationResultDto;

    private final WorkerApplicationRepository workerApplicationRepository;
    private final AgencyWorkerApplicationRepository agencyWorkerApplicationRepository;

    private final EmployerRepository employerRepository;
    private final EducationRepository educationRepository;
    private final ReferenceRepository referenceRepository;
    private final EmploymentRepository employmentRepository;
    private final DataBucketUtil dataBucketUtil;

    public WorkerApplicationServiceImpl(WorkerApplicationToWorkerApplicationResultDto toWorkerApplicationResultDto, WorkerApplicationRepository workerApplicationRepository, AgencyWorkerApplicationRepository agencyWorkerApplicationRepository, EmployerRepository employerRepository, EducationRepository educationRepository, ReferenceRepository referenceRepository, EmploymentRepository employmentRepository, DataBucketUtil dataBucketUtil) {
        this.toWorkerApplicationResultDto = toWorkerApplicationResultDto;
        this.workerApplicationRepository = workerApplicationRepository;
        this.agencyWorkerApplicationRepository = agencyWorkerApplicationRepository;
        this.employerRepository = employerRepository;
        this.educationRepository = educationRepository;
        this.referenceRepository = referenceRepository;
        this.employmentRepository = employmentRepository;
        this.dataBucketUtil = dataBucketUtil;
    }


    @Override
    public WorkerApplicationResultDto addWorkerApplication(WorkerApplicationCreateDto workerApplicationCreateDto) {
        WorkerApplication workerApplication = new WorkerApplication();
        WorkerApplication workerApplication1 = workerApplicationRepository.findByWorkerId(workerApplicationCreateDto.getWorkerId());

        if(workerApplication1!=null) {
            workerApplication =workerApplication1;
        }
        workerApplication.setWorkerId(workerApplicationCreateDto.getWorkerId());
        workerApplication.setPosition(workerApplicationCreateDto.getPosition());
        workerApplication.setLocation(workerApplicationCreateDto.getLocation());
        workerApplication.setHours(workerApplicationCreateDto.getHours());
        workerApplication.setClosingDate(workerApplicationCreateDto.getClosingDate());
        workerApplication.setTitle(workerApplicationCreateDto.getTitle());
        workerApplication.setLastname(workerApplicationCreateDto.getLastname());
        workerApplication.setFirstname(workerApplicationCreateDto.getFirstname());
        workerApplication.setPreferredName(workerApplicationCreateDto.getPreferredName());
//        workerApplication.setNationality(workerApplicationCreateDto.getNationality());
        workerApplication.setDob(workerApplicationCreateDto.getDob());
        workerApplication.setAddress(workerApplicationCreateDto.getAddress());
        workerApplication.setEmail(workerApplicationCreateDto.getEmail());
        workerApplication.setPhone(workerApplicationCreateDto.getPhone());
        workerApplication.setUkWork(workerApplicationCreateDto.getUkWork());
        workerApplication.setRestrictionRes(workerApplicationCreateDto.getRestrictionRes());
        workerApplication.setInsuaranceNum(workerApplicationCreateDto.getInsuaranceNum());
        workerApplication.setPersonalState(workerApplicationCreateDto.getPersonalState());
        workerApplication.setOffensesRes(workerApplicationCreateDto.getOffensesRes());
        workerApplication.setInvestigationsRes(workerApplicationCreateDto.getInvestigationsRes());
        workerApplication.setAvailability(workerApplicationCreateDto.getAvailability());
        workerApplication.setDbsCert(workerApplicationCreateDto.getDbsCert());
        workerApplication.setDbs(workerApplicationCreateDto.getDbs());
        workerApplication.setConfidential(workerApplicationCreateDto.getConfidential());
        workerApplication.setDbsRenewal(workerApplicationCreateDto.getDbsRenewal());
        workerApplication.setSigned(workerApplicationCreateDto.getSigned());
        workerApplication.setPrintName(workerApplicationCreateDto.getPrintName());
        workerApplication.setSignDate(workerApplicationCreateDto.getSignDate());
        workerApplication.setLicence(workerApplicationCreateDto.getLicence());
        workerApplication.setCar(workerApplicationCreateDto.getCar());
        workerApplication.setOnlyEmployment(workerApplicationCreateDto.getOnlyEmployment());
        workerApplication.setSanctionsRes(workerApplicationCreateDto.getSanctionsRes());
        workerApplication.setDismissedRes(workerApplicationCreateDto.getDismissedRes());
        workerApplication.setRelatedRes(workerApplicationCreateDto.getRelatedRes());
        workerApplication.setGenFullname(workerApplicationCreateDto.getGenFullname());
        workerApplication.setGenSigned(workerApplicationCreateDto.getGenSigned());
        workerApplication.setGenDate(workerApplicationCreateDto.getGenDate());
        workerApplication.setUnavailable(workerApplicationCreateDto.getUnavailable());
        workerApplication.setSubmitted(workerApplicationCreateDto.getSubmitted());

        workerApplication.setRestriction(workerApplicationCreateDto.getRestriction());
        workerApplication.setOffenses(workerApplicationCreateDto.getOffenses());
        workerApplication.setInvestigations(workerApplicationCreateDto.getInvestigations());
        workerApplication.setAvailability(workerApplicationCreateDto.getAvailability());
        workerApplication.setSanctions(workerApplicationCreateDto.getSanctions());
        workerApplication.setDismissed(workerApplicationCreateDto.getDismissed());
        workerApplication.setRelated(workerApplicationCreateDto.getRelated());

        workerApplication.setProfreg(workerApplicationCreateDto.getProfreg());
        workerApplication.setCertificate(workerApplicationCreateDto.getCertificate());

        workerApplication.setDbsNumber(workerApplicationCreateDto.getDbsNumber());
        workerApplication.setDbsType(workerApplicationCreateDto.getDbsType());
        workerApplication.setLastModifiedDate(now());

        workerApplication.setReferences(new HashSet<>(workerApplicationCreateDto.getReferences()));
        workerApplication.setEmployments(new HashSet<>(workerApplicationCreateDto.getEmployments()));
        workerApplication.setEmployers(new HashSet<>(workerApplicationCreateDto.getEmployers()));
        workerApplication.setEducations(new HashSet<>(workerApplicationCreateDto.getEducations()));

//        try{
            workerApplicationRepository.save(workerApplication);
//        }catch (Exception e){
//            throw new BusinessValidationException("Failed to automatically save. Try again.");
//        }



        return toWorkerApplicationResultDto.convert(workerApplication);
    }




    @Override
    public void addSafeguardingSignature(Long workerId, MultipartFile files){


        WorkerApplicationServiceImpl.log.info("Start file uploading service");
        List<WorkerApplication> inputFiles = new ArrayList<>();

        Arrays.asList(files).forEach(file -> {
            String originalFileName = file.getOriginalFilename();
            if(originalFileName == null){
                throw new BusinessValidationException("Original file city is null");
            }
            Path path = new File(originalFileName).toPath();

//            try {
            String contentType = null;
            try {
                contentType = Files.probeContentType(path);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            FileDto fileDto = dataBucketUtil.uploadFile(file, "w/f/"+ workerId +"/"+originalFileName, contentType);

                if (fileDto != null) {

                    WorkerApplication workerApplication = new WorkerApplication();
                    workerApplication.setWorkerId(workerId);

                    WorkerApplication workerApplication1 = workerApplicationRepository.findByWorkerId(workerId);

                    if(workerApplication1!=null) {
                        workerApplication =workerApplication1;
                    }


                    workerApplication.setSigned(fileDto.getFileUrl());
                    workerApplicationRepository.save(workerApplication);
//                    inputFiles.add(new InputFile(fileDto.getFileName(), fileDto.getFileUrl()));
                    WorkerApplicationServiceImpl.log.debug("File uploaded successfully, file city: {} and url: {}",fileDto.getFileName(), fileDto.getFileUrl() );
                }
//            } catch (Exception e) {
//                log.error("Error occurred while uploading. Error: ", e);
//
//                    throw new FileNotFoundException("Error occurred while uploading");
//
//            }
        });

//        fileRepository.saveAll(inputFiles);
        WorkerApplicationServiceImpl.log.debug("File details successfully saved in the database");






    }

    @Override
    public void updateWorkerApplication(WorkerApplicationCreateDto application) {
        AgencyWorkerApplication agencyWorkerApplication;

            agencyWorkerApplication = agencyWorkerApplicationRepository
                    .findByWorkerIdAndAgencyId(
                            application.getWorkerId(),
                            application.getAgencyId()
                    );

        if (agencyWorkerApplication   ==  null){

            agencyWorkerApplication = new AgencyWorkerApplication();
            agencyWorkerApplication.setAgencyId(application.getAgencyId());
            agencyWorkerApplication.setWorkerId(application.getWorkerId());

        }


        log.info("Updated worker-application{}", agencyWorkerApplication);


        agencyWorkerApplication.setComment(application.getComment());

        if (application.getStatus().equalsIgnoreCase(FormStatus.REJECTED.toString())){
            agencyWorkerApplication.setStatus(FormStatus.REJECTED);
        }

        if (application.getStatus().equalsIgnoreCase(FormStatus.APPROVED.toString())){
            agencyWorkerApplication.setStatus(FormStatus.APPROVED);
        }

        agencyWorkerApplicationRepository.save(agencyWorkerApplication);



    }


    @Override
    public void addGeneralSignature(Long workerId, MultipartFile files){

        WorkerApplicationServiceImpl.log.info("Start file uploading service");
        List<WorkerApplication> inputFiles = new ArrayList<>();

        Arrays.asList(files).forEach(file -> {
            String originalFileName = file.getOriginalFilename();
            if(originalFileName == null){
                throw new BusinessValidationException("Original file city is null");
            }
            Path path = new File(originalFileName).toPath();

//            try {
            String contentType = null;
            try {
                contentType = Files.probeContentType(path);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            FileDto fileDto = dataBucketUtil.uploadFile(file, "w/f/"+ workerId+"/" +originalFileName, contentType);

            if (fileDto != null) {
                WorkerApplication workerApplication = new WorkerApplication();
                workerApplication.setWorkerId(workerId);

                WorkerApplication workerApplication1 = workerApplicationRepository.findByWorkerId(workerId);

                if(workerApplication1!=null) {
                    workerApplication =workerApplication1;
                }

                workerApplicationRepository.save(workerApplication);
//                    inputFiles.add(new InputFile(fileDto.getFileName(), fileDto.getFileUrl()));
                WorkerApplicationServiceImpl.log.debug("File uploaded successfully, file city: {} and url: {}",fileDto.getFileName(), fileDto.getFileUrl() );
            }
//            } catch (Exception e) {
//                log.error("Error occurred while uploading. Error: ", e);
//
//                    throw new FileNotFoundException("Error occurred while uploading");
//
//            }
        });

//        fileRepository.saveAll(inputFiles);
        WorkerApplicationServiceImpl.log.debug("File details successfully saved in the database");






//        return toWorkerApplicationResultDto.convert(workerApplication);
    }


    @Override
    public void addApplicationDoc(Long workerId,Long compliance, MultipartFile file) {
        WorkerApplicationServiceImpl.log.info("Request to upload a payslip");
        String name = "application-"+compliance+".pdf";

        WorkerApplication workerApplication = getOne(compliance);


        List<String> types = new ArrayList<String>();

        types.add("application/pdf");
//        types.add("image/jpeg");
//        types.add("image/jpg");


        if (!types.contains(file.getContentType())) {
            throw new BusinessValidationException("Uploaded file type is not supported. Please upload a pdf");
        }

        if (!file.isEmpty() ) {
            try {
                byte[] bytes = file.getBytes();

                File dir = new File(rootPath + File.separator + "worker"+ File.separator + workerId  );

                if (!dir.exists())  dir.mkdirs();

                // Create the file on server
                File serverFile = new File(dir.getAbsolutePath()
                        + File.separator + name);
                BufferedOutputStream stream = new BufferedOutputStream(
                        new FileOutputStream(serverFile));
                stream.write(bytes);
                stream.close();


                WorkerApplicationServiceImpl.log.info("Server File Location="
                        + serverFile.getAbsolutePath());

                WorkerApplicationServiceImpl.log.info("Uploaded");
//                workerApplication.setUploaded(true);
                workerApplicationRepository.save(workerApplication);

            } catch (Exception e) {
                WorkerApplicationServiceImpl.log.info("You failed to upload");
                WorkerApplicationServiceImpl.log.error(e.getMessage());
                throw new BusinessValidationException("Upload failed.");

            }

        } else {
            WorkerApplicationServiceImpl.log.info("You failed to upload");
            throw new BusinessValidationException("Uploaded file is not empty.");

        }
    }

    @Override
    public void deleteWorkerApplication(Long id) {

        WorkerApplication availability = workerApplicationRepository.findById(id).get();

        workerApplicationRepository.delete(availability);
        workerApplicationRepository.flush();


    }

    @Override
    public WorkerApplication findById(Long id) {
        return null;
    }

    @Override
    public WorkerApplicationResultDto findWorkerApplications(Long workerId) {

        DateTimeFormatter pattern = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        WorkerApplication workerApplication = workerApplicationRepository.findByWorkerId(workerId);
        if(workerApplication==null){
            return null;
        }
        if(workerApplication.getLastModifiedDate()!=null)workerApplication.setLastModifiedDate1(workerApplication.getLastModifiedDate().format(pattern));


        if(workerApplication==null) {
            return null;
        }else{
            WorkerApplicationResultDto application = toWorkerApplicationResultDto.convert(workerApplication);
            return application;
        }

    }

    @Override
    public WorkerApplicationResultDto findAgencyWorkerApplication(Long workerId, Long agencyId) {
        DateTimeFormatter pattern = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        WorkerApplication workerApplication = workerApplicationRepository.findByWorkerId(workerId);

        if(workerApplication==null){
//            throw new RecordNotFoundException("Worker banking details not found");
            return null;
        }

//        log.info("Found worker application: {}", workerApplication.getLastModifiedDate());
        if(workerApplication.getLastModifiedDate()!=null)workerApplication.setLastModifiedDate1(workerApplication.getLastModifiedDate().format(pattern));


        AgencyWorkerApplication app;

        if(workerApplication==null) {
            return null;
        }else{


            app = agencyWorkerApplicationRepository.findByWorkerIdAndAgencyId(workerId, agencyId);

            if(app== null) {
                app = new AgencyWorkerApplication();
            }

            WorkerApplicationResultDto application = toWorkerApplicationResultDto.convert(workerApplication);

            if(app.getStatus()!=null) application.setStatus(app.getStatus().toString());
            if(app.getAgencyId()!=null) application.setAgencyId(app.getAgencyId());
            if(app.getComment()!=null) application.setComment(app.getComment());
            return application;
        }
    }

    @Override
    public List<WorkerApplicationResultDto> findAgencyWorkerForms(Long workerId, Long agencyId, PageRequest of) {

        Page<WorkerApplicationResultDto> iWorkerApplicationResultDto = workerApplicationRepository.findAllWorkerApplications2(workerId, of) .map(toWorkerApplicationResultDto::convert);;

        List<WorkerApplicationResultDto> list = iWorkerApplicationResultDto.getContent();

        return list;
    }



    @Override
    public Page<WorkerApplication> findAllPaged(PageRequest of) {
        return null;
    }

    @Override
    public WorkerApplication save(WorkerApplicationUpdateDto workerApplicationUpdateDto) {
        return null;
    }

    @Override
    public WorkerApplication getOne(Long id) {
        return workerApplicationRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Worker application not found"));
    }    
    
    public Education getOneEducation(Long id) {
        return educationRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Worker education not found"));
    }    
    public Employer getOneEmployer(Long id) {
        return employerRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Worker employer not found"));
    }    
    public Reference getOneReference(Long id) {
        return referenceRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Worker reference not found"));
    }   
    public Employment getOneEmployment(Long id) {
        return employmentRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Worker employent not found"));
    }


    @Override
    public List<IEmployerResultDto> findWorkerEmployers(Long workerId) {

        List<IEmployerResultDto> employerResultDto = employerRepository.findAllByWorkerApplicationId(workerId);

        return employerResultDto;
    }
    @Override
    public void deleteEmployer(Long id) {
        Employer employer = employerRepository.findById(id).get();
        employerRepository.delete(employer);
        employerRepository.flush();
    }


    @Override
    public List<IEducationResultDto> findWorkerEducations(Long workerId) {

        List<IEducationResultDto> educationResultDto = educationRepository.findAllByWorkerApplicationId(workerId);

        return educationResultDto;
    }
    @Override
    public void deleteEducation(Long id) {
        Education education = educationRepository.findById(id).get();
        educationRepository.delete(education);
        educationRepository.flush();
    }


    @Override
    public List<IEmploymentResultDto> findWorkerEmployments(Long workerId) {

        List<IEmploymentResultDto> employmentResultDto = employmentRepository.findAllByWorkerApplicationId(workerId);

        return employmentResultDto;
    }



    @Override
    public List<IReferenceResultDto> findWorkerReferences(Long applicationId) {

        List<IReferenceResultDto> referenceResultDto = referenceRepository.findAllByWorkerApplicationId(applicationId);

        return referenceResultDto;
    }



}

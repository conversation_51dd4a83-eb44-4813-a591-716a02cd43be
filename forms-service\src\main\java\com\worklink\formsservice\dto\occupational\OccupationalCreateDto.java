package com.worklink.formsservice.dto.occupational;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;

@Data
public class OccupationalCreateDto {


    private Long id;
    private String title;
    private String lastname;
    private String firstname;
    private String preferredName;
    private String nationality;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate dob;
    private String address;
    private String email;
    private String phone;
    private Boolean disability;
    private String disabilityRes;
    private Boolean disabilityByWork;
    private String disabilityByWorkRes;
    private Boolean currentTreatment;
    private String currentTreatmentRes;
    private Boolean assistance;
    private String assistanceRes;
    private Boolean mrsa;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate mrsaDate;
    private Boolean cdiff;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate cdiffDate;
    private Boolean chickenpox;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate chickenpoxDate;
    private Boolean bbv;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate bbvDate;
    private Boolean travelled;
    private Boolean bcg;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate bcgDate;
    private Boolean cough;
    private Boolean weight;
    private Boolean fever;
    private Boolean tb;
    private String tbcRes;
    private Boolean tripleVac;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate tripleVacDate;
    private Boolean polio;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate polioDate;
    private Boolean tetanus;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate tetanusDate;
    private Boolean hepb;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate c1;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate c2;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate c3;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate b1;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate b2;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate b3;
    private String varUp;
    private String tbUp;
    private String lastModifiedDate;
    private String rubUp;
    private String hepbUP;
    private String hepbsUp;
    private String hepcUp;
    private String hivUp;
    private Boolean exposure;
    private Boolean consent;
    private Boolean copy;
    private String fullname;
    private String sigUp;
    private Long workerId;
    private Long agencyId;
    private String comment;
    private String status;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate sigDate;
}

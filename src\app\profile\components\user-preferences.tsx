"use client";

import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/common/components/ui/card";
import { Button } from "@/common/components/ui/button";
import { Label } from "@/common/components/ui/label";
import { CommonCurrencySelector } from "@/common/components/currency-selector";
import {
  formatCurrency,
  getCurrencySymbol
} from "@/common/lib/currency-utils";
import { useUserCurrency } from "@/common/hooks/use-user-preferences";
import { useToast } from "@/common/hooks/use-toast";
import { Settings, Globe, DollarSign } from "lucide-react";

export function UserPreferences() {
  const { preferredCurrency, updatePreferredCurrency, isLoading, error } = useUserCurrency();
  const [tempCurrency, setTempCurrency] = useState<string>(preferredCurrency);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  // Update temp currency when preferred currency changes
  React.useEffect(() => {
    setTempCurrency(preferredCurrency);
  }, [preferredCurrency]);

  const handleCurrencyChange = (currencyCode: string) => {
    setTempCurrency(currencyCode);
  };

  const handleSavePreferences = async () => {
    setIsSaving(true);
    try {
      await updatePreferredCurrency(tempCurrency);

      toast({
        title: "Preferences saved",
        description: `Your preferred currency has been set to ${tempCurrency}`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save preferences. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const getCurrentCurrencyInfo = () => {
    const commonCurrencies = [
      { code: 'USD', name: 'US Dollar', symbol: '$' },
      { code: 'EUR', name: 'Euro', symbol: '€' },
      { code: 'GBP', name: 'British Pound', symbol: '£' },
      { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
      { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
      { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
      { code: 'CHF', name: 'Swiss Franc', symbol: 'CHF' },
      { code: 'CNY', name: 'Chinese Yuan', symbol: '¥' },
      { code: 'SEK', name: 'Swedish Krona', symbol: 'kr' },
      { code: 'NOK', name: 'Norwegian Krone', symbol: 'kr' }
    ];
    
    return commonCurrencies.find(c => c.code === tempCurrency) ||
           { code: tempCurrency, name: tempCurrency, symbol: getCurrencySymbol(tempCurrency) };
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  const currencyInfo = getCurrentCurrencyInfo();

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Settings className="h-5 w-5" />
        <h2 className="text-xl font-semibold">User Preferences</h2>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Currency Preferences
          </CardTitle>
          <CardDescription>
            Choose your preferred currency for viewing vehicle prices and booking costs.
            This setting only affects how prices are displayed to you.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center gap-3">
                <DollarSign className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium text-blue-900">Current Currency</p>
                  <p className="text-sm text-blue-700">
                    {currencyInfo.code} - {currencyInfo.name} ({currencyInfo.symbol})
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm text-blue-600">Example:</p>
                <p className="font-semibold text-blue-900">
                  {formatCurrency(50, tempCurrency)}/day
                </p>
              </div>
            </div>

            <div className="space-y-3">
              <Label htmlFor="currency-selector">Select Preferred Currency</Label>
              <CommonCurrencySelector
                selectedCurrency={tempCurrency}
                onCurrencyChange={handleCurrencyChange}
                className="w-full"
              />
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-800 mb-2">Important Notes:</h4>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• Prices are converted using real-time exchange rates from Stripe</li>
                <li>• Your preferred currency only affects price display, not payment currency</li>
                <li>• Payments are processed in the vehicle owner's base currency</li>
                <li>• Exchange rates are updated hourly for accuracy</li>
              </ul>
            </div>

            <div className="flex justify-end pt-4">
              <Button 
                onClick={handleSavePreferences}
                disabled={isSaving}
                className="min-w-[120px]"
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Saving...
                  </>
                ) : (
                  "Save Preferences"
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

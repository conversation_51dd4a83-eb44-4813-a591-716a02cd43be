name: Deploy

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up JDK 11
        uses: actions/setup-java@v2
        with:
          distribution: 'adopt'
          java-version: '11'

      - name: Build with <PERSON><PERSON>
        run: mvn clean install -DskipTests

      - name: Install Docker Compose
        run: |
          sudo apt-get update
          sudo apt-get install -y docker-compose
          

      - name: Export Environment Variables
        run: |
          echo "STRIPE_SECRET_KEY=${{ secrets.STRIPE_SECRET_KEY }}" >> $GITHUB_ENV
          echo "STRIPE_PUBLIC_KEY=${{ secrets.STRIPE_PUBLIC_KEY }}" >> $GITHUB_ENV
          echo "STRIPE_WEBHOOK_SECRET=${{ secrets.STRIPE_WEBHOOK_SECRET }}" >> $GITHUB_ENV
          echo "PAYNOWINTEGRATIONID=${{ secrets.PAYNOWINTEGRATIONID }}" >> $GITHUB_ENV
          echo "PAYNOWINTEGRATIONKEY=${{ secrets.PAYNOWINTEGRATIONKEY }}" >> $GITHUB_ENV
          
          

      - name: Log in to Docker Hub
        run: echo "${{ secrets.DOCKER_HUB_PASSWORD }}" | docker login --username ${{ secrets.DOCKER_HUB_USERNAME }} --password-stdin

      - name: Build and Push Docker Image
        run: docker-compose build && docker-compose push
        env:
          STRIPE_SECRET_KEY: ${{ secrets.STRIPE_SECRET_KEY }}
          STRIPE_PUBLIC_KEY: ${{ secrets.STRIPE_PUBLIC_KEY }}
          STRIPE_WEBHOOK_SECRET: ${{ secrets.STRIPE_WEBHOOK_SECRET }}
          PAYNOWINTEGRATIONID: ${{ secrets.PAYNOWINTEGRATIONID }}
          PAYNOWINTEGRATIONKEY: ${{ secrets.PAYNOWINTEGRATIONKEY }}

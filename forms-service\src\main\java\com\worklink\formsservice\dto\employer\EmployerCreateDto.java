package com.worklink.formsservice.dto.employer;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;


@Data
public class EmployerCreateDto {

    private String job;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate start;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate end;
    private String description;
    private String notice;
    private String name;
    private String address;
    private String salary;
    private Long workerApplicationId;

}

package com.worklink.formsservice.model;

import lombok.AllArgsConstructor;
import lombok.Data;

import javax.persistence.*;
import java.time.LocalDate;
import java.util.List;


@Entity
@AllArgsConstructor
@Data
public class Occupational extends AbstractAuditingEntity{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String title;
    private String lastname;
    private String firstname;
    private String preferredName;
    private String nationality;
    private LocalDate dob;
    private String address;
    private String email;
    private String phone;

    private Boolean disability;
    private String disabilityRes;
    private Boolean disabilityByWork;
    private String disabilityByWorkRes;
    private Boolean currentTreatment;
    private String currentTreatmentRes;
    private Boolean assistance;
    private String assistanceRes;
    private Boolean mrsa;
    private LocalDate mrsaDate;
    private Boolean cdiff;
    private LocalDate cdiffDate;


    private Boolean chickenpox;
    private LocalDate chickenpoxDate;

    private Boolean bbv;
    private LocalDate bbvDate;

    private Boolean travelled;

    private Boolean bcg;
    private LocalDate bcgDate;


    private Boolean cough;
    private Boolean weight;
    private Boolean fever;
    private Boolean tb;
    private String tbcRes;


    private Boolean tripleVac;
    private LocalDate tripleVacDate;
    private Boolean polio;
    private LocalDate polioDate;
    private Boolean tetanus;
    private LocalDate tetanusDate;
    private Boolean hepb;
    private LocalDate c1;
    private LocalDate c2;
    private LocalDate c3;
    private LocalDate b1;
    private LocalDate b2;
    private LocalDate b3;


    private String varUp;
    private String tbUp;
    private String rubUp;
    private String hepbUP;

    private  String hepbsUp;
    private  String hepcUp;
    private  String hivUp;


    private Boolean exposure;

    private Boolean consent;
    private Boolean copy;

    private String fullname;
    private String sigUp;
    private LocalDate sigDate;

    @Column(unique = true, nullable = false)
    private Long workerId;

//    @OneToOne(fetch = FetchType.LAZY)
//    private Worker worker;

    public Occupational() {};


}

package com.worklink.formsservice.dto.education;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;


@Data
public class EducationCreateDto {
    private Long id;
    private String name;
    private String qualification;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate start;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate end;
    private Long workerApplicationId;

}

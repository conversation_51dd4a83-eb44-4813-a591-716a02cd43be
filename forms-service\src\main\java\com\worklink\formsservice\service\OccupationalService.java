package com.worklink.formsservice.service;


import com.worklink.formsservice.dto.education.EducationsCreateDto;
import com.worklink.formsservice.dto.education.IEducationResultDto;
import com.worklink.formsservice.dto.employer.EmployersCreateDto;
import com.worklink.formsservice.dto.employer.IEmployerResultDto;
import com.worklink.formsservice.dto.employment.EmploymentsCreateDto;
import com.worklink.formsservice.dto.employment.IEmploymentResultDto;
import com.worklink.formsservice.dto.occupational.OccupationalCreateDto;
import com.worklink.formsservice.dto.occupational.OccupationalResultDto;
import com.worklink.formsservice.dto.reference.IReferenceResultDto;
import com.worklink.formsservice.dto.reference.ReferencesCreateDto;
import com.worklink.formsservice.model.Occupational;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface OccupationalService {
    OccupationalCreateDto addOccupational(OccupationalCreateDto occupationalCreateDto);


    void deleteOccupational(Long id);

    Occupational findById(Long id);

    OccupationalResultDto findOccupationals(Long workerId);

    OccupationalResultDto findAgencyWorkerOccupational(Long workerId, Long agencyId);

    Page<Occupational> findAllPaged(PageRequest of);

    Occupational findByWorkerId(Long id);

    Occupational save(OccupationalCreateDto occupationalUpdateDto);

    Occupational getOne(Long id);

    void addApplicationDoc(Long workerId, Long compliance, MultipartFile file);

    void updateWorkerOccupational(OccupationalCreateDto occupational);

    void addHiv(Long workerId, MultipartFile file);

    void addHepc(Long workerId, MultipartFile file);

    void addHepbs(Long workerId, MultipartFile file);

    void addHepb(Long workerId, MultipartFile file);

    void addMmr(Long workerId, MultipartFile file);

    void addTb(Long workerId, MultipartFile file);

    void addVaricella(Long workerId, MultipartFile file);

    void addSignature(Long workerId, MultipartFile file);
}

package com.worklink.formsservice.dto.workerapplication;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.Instant;
import java.time.LocalDate;

@Data
public class WorkerApplicationUpdateDto {

    private Long id;
    private String position;
    private String location;
    private String hours;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate closingDate;
    private String title;
    private String lastname;
    private String firstname;
    private String preferredName;
    private String nationality;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate dob;
    private String address;
    private String email;
    private String phone;
    private Boolean ukWork;
    private String restrictionRes;
    private String insuaranceNum;
    private String personalState;
    private String offensesRes;
    private String investigationsRes;
    private Boolean dbsCert;
    private Boolean dbs;
    private LocalDate dbsRenewal;
    private Boolean signed;
    private String printName;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate signDate;
    private Boolean licence;
    private Boolean car;
    private Boolean onlyEmployment;
    private String sanctionsRes;
    private String dismissedRes;
    private String relatedRes;
    private String genFullname;
    private Boolean genSigned;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate genDate;
    private String unavailable;
    private Boolean submitted;
    private Long workerId;


    private Boolean restriction;
    private Boolean offenses;
    private Boolean investigations;
    private Boolean availability;
    private Boolean sanctions;
    private Boolean dismissed;
    private Boolean related;
    private String lastModifiedDate;


    private String profreg;
    private String certificate;


}

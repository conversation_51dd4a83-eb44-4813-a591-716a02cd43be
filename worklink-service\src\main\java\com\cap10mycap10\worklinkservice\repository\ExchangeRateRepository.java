package com.cap10mycap10.worklinkservice.repository;

import com.cap10mycap10.worklinkservice.model.ExchangeRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface ExchangeRateRepository extends JpaRepository<ExchangeRate, Long> {

    /**
     * Find the latest exchange rate between two currencies
     */
    @Query("SELECT er FROM ExchangeRate er WHERE er.fromCurrency = :fromCurrency " +
           "AND er.toCurrency = :toCurrency AND er.isActive = true " +
           "ORDER BY er.rateDate DESC")
    Optional<ExchangeRate> findLatestRate(@Param("fromCurrency") String fromCurrency, 
                                         @Param("toCurrency") String toCurrency);

    /**
     * Find exchange rates that are older than the specified date
     */
    @Query("SELECT er FROM ExchangeRate er WHERE er.rateDate < :cutoffDate AND er.isActive = true")
    List<ExchangeRate> findRatesOlderThan(@Param("cutoffDate") LocalDateTime cutoffDate);

    /**
     * Find all active exchange rates for a specific currency pair
     */
    List<ExchangeRate> findByFromCurrencyAndToCurrencyAndIsActiveTrueOrderByRateDateDesc(
            String fromCurrency, String toCurrency);

    /**
     * Find the most recent exchange rate within a time window
     */
    @Query("SELECT er FROM ExchangeRate er WHERE er.fromCurrency = :fromCurrency " +
           "AND er.toCurrency = :toCurrency AND er.isActive = true " +
           "AND er.rateDate >= :fromDate ORDER BY er.rateDate DESC")
    Optional<ExchangeRate> findLatestRateAfter(@Param("fromCurrency") String fromCurrency,
                                              @Param("toCurrency") String toCurrency,
                                              @Param("fromDate") LocalDateTime fromDate);

    /**
     * Delete old exchange rates to keep the table clean
     */
    void deleteByRateDateBeforeAndIsActiveTrue(LocalDateTime cutoffDate);
}

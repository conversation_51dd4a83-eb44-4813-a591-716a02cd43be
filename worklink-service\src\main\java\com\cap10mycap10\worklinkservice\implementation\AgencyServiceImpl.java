package com.cap10mycap10.worklinkservice.implementation;


import com.cap10mycap10.worklinkservice.dao.*;
import com.cap10mycap10.worklinkservice.dto.agency.AgencyCreateDto;
import com.cap10mycap10.worklinkservice.dto.agency.AgencyResultDto;
import com.cap10mycap10.worklinkservice.dto.agency.AgencyStats;
import com.cap10mycap10.worklinkservice.dto.agency.AgencyUpdateDto;
import com.cap10mycap10.worklinkservice.dto.client.ClientDto;
import com.cap10mycap10.worklinkservice.dto.file.FileDto;
import com.cap10mycap10.worklinkservice.dto.shift.BookingResultDto;
import com.cap10mycap10.worklinkservice.dto.worker.WorkerResultDto;
import com.cap10mycap10.worklinkservice.enums.AgencyType;
import com.cap10mycap10.worklinkservice.enums.ShiftStatus;
import com.cap10mycap10.worklinkservice.enums.Status;
import com.cap10mycap10.worklinkservice.enums.WorkerStatus;
import com.cap10mycap10.worklinkservice.exception.FileNotFoundException;
import com.cap10mycap10.worklinkservice.feign.RegisterAgentAdminFeignClient;
import com.cap10mycap10.worklinkservice.feigndtos.feigndtos.request.UserDto;
import com.cap10mycap10.worklinkservice.feigndtos.feigndtos.response.UserResponse;
import com.cap10mycap10.worklinkservice.helpers.DataBucketUtil;
import com.cap10mycap10.worklinkservice.events.email.EmailService;
import com.cap10mycap10.worklinkservice.mapper.agency.AgencyDtoToAgency;
import com.cap10mycap10.worklinkservice.mapper.agency.AgencyToAgencyResultDto;
import com.cap10mycap10.worklinkservice.mapper.client.ClientToClientDto;
import com.cap10mycap10.worklinkservice.mapper.shift.ShiftToShiftResultDto;
import com.cap10mycap10.worklinkservice.mapper.worker.WorkerToWorkerResultDto;
import com.cap10mycap10.worklinkservice.model.*;
import com.cap10mycap10.worklinkservice.model.Address;
import com.cap10mycap10.worklinkservice.service.AgencyService;
import com.cap10mycap10.worklinkservice.service.ClientService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import org.springframework.web.multipart.MultipartFile;
import javax.persistence.EntityNotFoundException;
import javax.persistence.NoResultException;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Objects.nonNull;


@Service
@Slf4j
public class AgencyServiceImpl implements AgencyService {
    @Value("${storage.volume.path}")
    private  String rootPath;
    private final AgencyRepository agencyRepository;

    @Autowired
    private ClientService clientService;

    private final AgencyWorkerPropertiesRepository agencyWorkerPropertiesRepository;
    private final AgencyDtoToAgency toAgency;
    private final AgencyToAgencyResultDto toAgencyResultDto;
    private final RegisterAgentAdminFeignClient registerAgentAdminFeignClient;
    private final ShiftCriteriaQuery shiftCriteriaQuery;
    private final WorkerRepository workerRepository;

    private final WorkerToWorkerResultDto toWorkerResultDto;
    private final ShiftRepository shiftRepository;
    private final ClientRepository clientRepository;
    private final DataBucketUtil dataBucketUtil;
    private final ShiftToShiftResultDto toShiftResultDto;
    private final EmailService emailService;
    private ObjectMapper objectMapper = new ObjectMapper();
    @Autowired
    private ClientToClientDto toClientDto;

    public AgencyServiceImpl(final AgencyRepository agencyRepository,
                             AgencyWorkerPropertiesRepository agencyWorkerPropertiesRepository, final AgencyDtoToAgency toAgency,
                             final AgencyToAgencyResultDto toAgencyResultDto,
                             RegisterAgentAdminFeignClient registerAgentAdminFeignClient,
                             ShiftCriteriaQuery shiftCriteriaQuery,
                             WorkerRepository workerRepository, WorkerToWorkerResultDto toWorkerResultDto,
                             ShiftRepository shiftRepository,
                             ClientRepository clientRepository,
                             DataBucketUtil dataBucketUtil, ShiftToShiftResultDto toShiftResultDto,
                             EmailService emailService
    ) {
        this.agencyRepository = agencyRepository;
        this.agencyWorkerPropertiesRepository = agencyWorkerPropertiesRepository;
        this.toAgency = toAgency;
        this.toAgencyResultDto = toAgencyResultDto;
        this.registerAgentAdminFeignClient = registerAgentAdminFeignClient;

        // this.approvedAgencyRepository = approvedAgencyRepository;
        this.shiftCriteriaQuery = shiftCriteriaQuery;
        this.workerRepository = workerRepository;
        this.toWorkerResultDto = toWorkerResultDto;
        this.shiftRepository = shiftRepository;
        this.clientRepository = clientRepository;
        this.dataBucketUtil = dataBucketUtil;
        this.toShiftResultDto = toShiftResultDto;
        this.emailService = emailService;
        //this.workerAgencyRepository = workerAgencyRepository;
    }

    @Override
    public void save(Agency agency) {
        agencyRepository.save(agency);
    }


    @Override
    public void save(AgencyCreateDto agencyCreateDto) throws JsonProcessingException {
        agencyCreateDto.setEmail(agencyCreateDto.getAdministratorCreateDto().getAdminEmail());
        Agency agency = agencyRepository.findByEmail(agencyCreateDto.getAdministratorCreateDto().getAdminEmail());
        Client client = clientService.saveClient(agencyCreateDto.toClientDto());
        if(nonNull(agency)) {
            throw new BusinessValidationException("Account already registered, try resetting your password");
        }
            agency = agencyRepository.save(toAgency.convert(agencyCreateDto));
            registerUser(agencyCreateDto, agency, client);
    }

    public void registerUser(AgencyCreateDto agencyCreateDto, Agency agency, Client client) {

        UserDto userCreationDto = new UserDto();
        userCreationDto.setFirstName(agencyCreateDto.getAdministratorCreateDto().getFirstname());
        userCreationDto.setLastName(agencyCreateDto.getAdministratorCreateDto().getLastname());
        userCreationDto.setEmail(agencyCreateDto.getAdministratorCreateDto().getAdminEmail());
        userCreationDto.setUsername(agencyCreateDto.getAdministratorCreateDto().getAdminEmail());
        if(agencyCreateDto.getAgencyType()== AgencyType.DEFAULT)userCreationDto.setRoleId(3L);
        if(agencyCreateDto.getAgencyType()== AgencyType.TRAINER)userCreationDto.setRoleId(100L);
        if(agencyCreateDto.getAgencyType()== AgencyType.TRANSPORTER)userCreationDto.setRoleId(101L);
        if(agencyCreateDto.getAgencyType()== AgencyType.TRANSPORTER)userCreationDto.setRoleId(101L);
        if(agencyCreateDto.getAgencyType()== AgencyType.DEFAULT)userCreationDto.setRoleId(3L);
        userCreationDto.setUserType("AGENCY");
        userCreationDto.setAgentId(agency.getId());
        if(nonNull(client))userCreationDto.setClientId(client.getId());

        registerAgentAdminFeignClient.registerUserAccountFeign(userCreationDto);

        log.info("Response add agency: {}", agency);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(AgencyUpdateDto agencyUpdateDto) {
        try {
            Agency agencyOptional = getOne(agencyUpdateDto.getAgencyId());

            agencyOptional.setAddress(agencyUpdateDto.getAddress());
            agencyOptional.setTelephone(agencyUpdateDto.getTelephone());
            agencyOptional.setName(agencyUpdateDto.getName());
            agencyOptional.setBillingEmail(agencyUpdateDto.getBillingEmail());
            log.info("Response add agency: {}", agencyOptional);
            agencyRepository.save(agencyOptional);
        } catch (Exception exception) {
            throw new BusinessValidationException(exception.getMessage());
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBankDetails(Long agencyId, BankDetails bankDetails) {
        try {
            Agency agency  = getOne(agencyId);
            agency.setBankDetails(bankDetails);

            log.info("Response add agency: {}", agency );
            agencyRepository.save(agency );
        } catch (Exception exception) {
            throw new BusinessValidationException(exception.getMessage());
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBaseCurrency(Long agencyId, String baseCurrency) {
        try {
            Agency agency = getOne(agencyId);

            // Validate currency code format
            if (baseCurrency == null || !baseCurrency.matches("^[A-Z]{3}$")) {
                throw new BusinessValidationException("Invalid currency code format. Must be 3 uppercase letters.");
            }

            agency.setBaseCurrency(baseCurrency.toUpperCase());
            agencyRepository.save(agency);

            log.info("Updated base currency for agency {}: {}", agencyId, baseCurrency);
        } catch (Exception exception) {
            throw new BusinessValidationException("Failed to update base currency: " + exception.getMessage());
        }
    }

    @Override
    public AgencyResultDto findById(Long id) {
        return toAgencyResultDto.convert(agencyRepository.findById(id).orElseThrow(
                () -> new EntityNotFoundException("Agency does not exist")));
    }

    @Override
    public List<AgencyResultDto> findAll() {
        return agencyRepository.findAll()
                .stream()
                .map(toAgencyResultDto::convert)
                .collect(Collectors.toList());
    }

    @Override
    public Page<AgencyResultDto> findAllPaged(AgencyType agencyTypeFilter, String searchQuery, PageRequest of) {
        var name = nonNull(agencyTypeFilter) ? agencyTypeFilter.name():null;
        return agencyRepository.findAgenciesWithFilters(name, searchQuery, of)
                .map(toAgencyResultDto::convert);
    }

    @Override
    public Page<AgencyResultDto> findAllDefaultPaged(PageRequest of) {
        return agencyRepository.findAllByAgencyType(AgencyType.DEFAULT, of)
                .map(toAgencyResultDto::convert);
    }

    @Override
    public void deleteById(Long id) {
        Agency agency = agencyRepository.findById(id).get();
        try {
            agencyRepository.deleteById(id);
            agencyRepository.flush();
        } catch (Exception ex) {
            throw new BusinessValidationException("Agency cannot be deleted");
        }
    }

    @Override
    @Transactional(propagation= Propagation.REQUIRED)
    public Agency getOne(Long id) {
        return agencyRepository.findById(id)
                .orElseThrow(() -> new BusinessValidationException("Agency does not exist"));
    }

    @Override
    public Page<WorkerResultDto> findAllWorkersPaged(Long agencyId, PageRequest of) {
        Page<Worker> workerList  = workerRepository.findWorkersByAgency(agencyId, of);
        workerList.forEach(w->w.setActiveAgency(agencyId));
        return workerList.map(toWorkerResultDto::convert);
    }



    @Override
    public Page<WorkerResultDto> findAllApplicantsPaged(Long agencyId, PageRequest of) {
        List<WorkerResultDto> agentList = new ArrayList<>();
        Page<Worker> agentList2=  workerRepository.findApplicantForAnAgency(agencyId, of);
        agentList2.forEach(w->w.setActiveAgency(agencyId));
        List<WorkerResultDto> tres = agentList2.stream().filter(w -> w.getStatus() == null || w.getStatus() == WorkerStatus.APPLICANT).map(toWorkerResultDto::convert).collect(Collectors.toList());
        return PaginationUtil.paginateIWorker(of, tres);
    }


    @Override
    public Page<WorkerResultDto> findAllWorkersPagedPending(Long agencyId, PageRequest of) {
        return workerRepository.findWorkerForAnAgencyPendingShifts(agencyId, ShiftStatus.BILLED, of).map(toWorkerResultDto::convert);

    }

    @Override
    public Page<BookingResultDto> findAllShiftsAgencyPaged(Long agencyId, PageRequest of, LocalDate startDate, LocalDate endDate) {
        LocalDateTime startDateTime = LocalDateTime.of(startDate, LocalTime.of(0, 0));

        LocalDateTime endDateTime = LocalDateTime.of(endDate, LocalTime.of(23, 59));
        return shiftRepository.findShiftByAgencyId(agencyId, startDateTime, endDateTime, of)
                .map(toShiftResultDto::convert);

    }

    @Override
    public List<AgencyResultDto> findAllShifts() {
        return agencyRepository
                .findAll()
                .stream()
                .map(toAgencyResultDto::convert)
                .collect(Collectors.toList());
    }
    @Override
    public List<AgencyResultDto> findTrainers() {
        return agencyRepository
                .findAllByIsTrainer(true)
                .stream()
                .map(toAgencyResultDto::convert)
                .collect(Collectors.toList());
    }

    @Override
    public Integer findNumberOfAgencies() {
        return agencyRepository.findNumberOfAgents();
    }



    @Override
    public Page<BookingResultDto> findAllShiftsAgencyPaged(Long agencyId, PageRequest of, LocalDate startDate, LocalDate endDate, String status) {
        LocalDateTime startDateTime = LocalDateTime.of(startDate, LocalTime.of(0, 0));

        LocalDateTime endDateTime = LocalDateTime.of(endDate, LocalTime.of(23, 59));
        return shiftRepository.findShiftByAgencyId(agencyId, startDateTime, endDateTime, status, of)
                .map(toShiftResultDto::convert);
    }

    @Override
    @Transactional
    public Page<BookingResultDto> findAllShiftsAgencyPagedByStatus(Long agencyId, PageRequest of, String status, Long clientId, LocalDate startDate, LocalDate endDate) {
        log.info("Shift applicants for applied page in agency status: {}", status);

        List<Shift> agentList = null;
        try {
            if(status.equalsIgnoreCase(ShiftStatus.NEW.toString())){
                agentList = shiftRepository.findNewShiftsByAgencyId(agencyId, status);

            }
            else if(status.equalsIgnoreCase(ShiftStatus.CANCELLED.toString())||status.equalsIgnoreCase(ShiftStatus.BILLED.toString())){
                log.info("Shift applicants for applied page in agency status not New: {}", status);
                LocalDate date;
                date =  LocalDate.now().minusDays(30);

                agentList = shiftRepository.findShiftByAgencyIdAndStatusAndDate(agencyId, ShiftStatus.CANCELLED.toString(), date);
                agentList.addAll(shiftRepository.findShiftByAgencyIdAndStatusAndDate(agencyId, ShiftStatus.BILLED.toString(), date));


            }        else if(status.equalsIgnoreCase(ShiftStatus.AUTHORIZED.toString())){
                log.info("Shift applicants for applied page in agency status not New: {}", status);

                agentList = shiftRepository.findAgencyAuthorized(agencyId, status);
            }
            else{
                log.info("Shift applicants for applied page in agency status not New: {}", status);
                agentList = shiftRepository.findShiftByAgencyIdAndStatus(agencyId, status);
            }

        } catch (NoResultException nre) {

        }



        //filters
        if(startDate!=null) agentList = agentList.stream()
                .filter(p -> checkAfter(p.getStart().toString(), startDate))
                .collect(Collectors.toList());

        if(endDate!=null) agentList = agentList.stream()
                .filter(p -> checkBefore(p.getStart().toString(), endDate))
                .collect(Collectors.toList());

        if(clientId!=null) agentList = agentList.stream()
                    .filter(p -> Objects.equals(p.getClient().getId(), clientId))
                    .collect(Collectors.toList());




        Page<Shift> page = PaginationUtil.paginateShift(of, agentList);
        return page.map(toShiftResultDto::convert);
    }

    @Override
    public Page<BookingResultDto> findAllReleasedShift(Long agencyId, PageRequest of) {

        Page<Shift> agentList = null;


        agentList = shiftRepository.findAllByAgencyAndReleased(getOne(agencyId), true, of);

        return agentList.map(toShiftResultDto::convert);
    }



    @Override
    public AgencyStats getStats(Long id) {
        /*AgencyStats agencyStats = new AgencyStats();
        int numberOfWorkers = workerAgencyRepository.countWorkerAgenciesByAgency_Id(id);
        int numberOfClient = approvedAgencyRepository.countApprovedAgenciesByAgency_Id(id);
        List<Long> clients = approvedAgencyRepository.findClientIds(id);
        int numberOfShifts = 0;
        for (Long client : clients
        ) {
            numberOfShifts = +shiftRepository.findNumberOfShiftsByClient(client);
        }
        agencyStats.setNumberOfRegisteredClients(numberOfClient);
        agencyStats.setNumberOfRegisteredWorkers(numberOfWorkers);
        agencyStats.setNumberOfShifts(numberOfShifts);
        return agencyStats;*/
        return null;
    }

    @Override
    public BookingResultDto removeWorker(Long shiftId, Long workerId) {
        Shift shift = shiftRepository.findShiftByIdAndWorkerId(shiftId, workerId)
                .orElseThrow(() -> new EntityNotFoundException("Shift does not exist"));
        shift.setStatus(ShiftStatus.NEW);
        return toShiftResultDto.convert(shiftRepository.save(shift));
    }

    @Override
    @Transactional
    public void activate(Long id) {
        Agency agency = getOne(id);

        // Get all users for this agency before activating
        try {
            List<UserResponse> agencyUsers = registerAgentAdminFeignClient.getAgencyUsers(0, 1000, id);
            List<String> userEmails = agencyUsers.stream()
                    .map(UserResponse::getEmail)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // Activate the agency
            agency.setStatus(Status.ACTIVE);
            agencyRepository.save(agency);

            // Activate all agency users
            registerAgentAdminFeignClient.activateAgentUsers(id, true);

            // Send activation emails to all agency users
            if (!userEmails.isEmpty()) {
                sendActivationEmails(userEmails, agency.getName());
            }

            log.info("Agency {} activated successfully. {} users notified.", agency.getName(), userEmails.size());

        } catch (Exception e) {
            log.error("Error occurred while activating agency users or sending emails: {}", e.getMessage());
            // Still activate the agency even if user operations fail
            agency.setStatus(Status.ACTIVE);
            agencyRepository.save(agency);
            throw new BusinessValidationException("Agency activated but there was an issue with user notifications: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void deactivate(Long id) {
        Agency agency = getOne(id);

        // Get all users for this agency before deactivating
        try {
            List<UserResponse> agencyUsers = registerAgentAdminFeignClient.getAgencyUsers(0, 1000, id);
            List<String> userEmails = agencyUsers.stream()
                    .map(UserResponse::getEmail)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // Deactivate the agency
            agency.setStatus(Status.INACTIVE);
            agencyRepository.save(agency);

            // Deactivate all agency users
            registerAgentAdminFeignClient.activateAgentUsers(id, false);

            // Send deactivation emails to all agency users
            if (!userEmails.isEmpty()) {
                sendDeactivationEmails(userEmails, agency.getName());
            }

            log.info("Agency {} deactivated successfully. {} users notified.", agency.getName(), userEmails.size());

        } catch (Exception e) {
            log.error("Error occurred while deactivating agency users or sending emails: {}", e.getMessage());
            // Still deactivate the agency even if user operations fail
            agency.setStatus(Status.INACTIVE);
            agencyRepository.save(agency);
            throw new BusinessValidationException("Agency deactivated but there was an issue with user notifications: " + e.getMessage());
        }
    }

    private void sendDeactivationEmails(List<String> userEmails, String agencyName) {
        String subject = "Account Deactivated - " + agencyName;
        String message = String.format(
            "Dear User,\n\n" +
            "We regret to inform you that your account with %s has been deactivated by the system administrator.\n\n" +
            "You will no longer be able to access your account or use the platform services.\n\n" +
            "If you believe this is an error or need assistance, please contact our support team immediately.\n\n" +
            "We apologize for any inconvenience this may cause.\n\n",
            agencyName
        );

        try {
            emailService.sendEmailAsUser(userEmails, subject, message);
            log.info("Deactivation emails sent to {} users for agency: {}", userEmails.size(), agencyName);
        } catch (Exception e) {
            log.error("Failed to send deactivation emails for agency {}: {}", agencyName, e.getMessage());
        }
    }

    private void sendActivationEmails(List<String> userEmails, String agencyName) {
        String subject = "Account Activated - " + agencyName;
        String message = String.format(
            "Dear User,\n\n" +
            "Great news! Your account with %s has been activated by the system administrator.\n\n" +
            "You can now access your account and use all platform services. Please log in to your account to get started.\n\n" +
            "If you experience any issues accessing your account or need assistance, please contact our support team.\n\n" +
            "Welcome back to the platform!\n\n",
            agencyName
        );

        try {
            emailService.sendEmailAsUser(userEmails, subject, message);
            log.info("Activation emails sent to {} users for agency: {}", userEmails.size(), agencyName);
        } catch (Exception e) {
            log.error("Failed to send activation emails for agency {}: {}", agencyName, e.getMessage());
        }
    }

    @Override
    public void addLogo(Long agencyId, MultipartFile files) {
        List<String> types = new ArrayList<String>();

        types.add("image/png");
        types.add("image/jpeg");
        types.add("image/jpg");


        if (!types.contains(files.getContentType())) {
            throw new BusinessValidationException("Uploaded file type is not supported. Please upload images png, jpeg or jpg only.");
        }




        log.info("Start file uploading service");
        List<Agency> inputFiles = new ArrayList<>();

        Arrays.asList(files).forEach(file -> {
            String originalFileName = file.getOriginalFilename();
            if(originalFileName == null){
                throw new BusinessValidationException("Original file city is null");
            }
            Path path = new File(originalFileName).toPath();

            try {
                String contentType = Files.probeContentType(path);
                FileDto fileDto = dataBucketUtil.uploadFile(file, "/a/"+ agencyId+"/"+originalFileName, contentType);

                if (fileDto != null) {
                    Agency agency = getOne(agencyId);
                    agency.setLogo(fileDto.getFileUrl());
                    agencyRepository.save(agency);
//                    inputFiles.add(new InputFile(fileDto.getFileName(), fileDto.getFileUrl()));
                    log.debug("File uploaded successfully, file city: {} and url: {}",fileDto.getFileName(), fileDto.getFileUrl() );
                }
            } catch (Exception e) {
                log.error("Error occurred while uploading. Error: ", e);
                throw new FileNotFoundException("Error occurred while uploading");
            }
        });

//        fileRepository.saveAll(inputFiles);
        log.debug("File details successfully saved in the database");
    }

    @Override
    @Transactional
    public Page<BookingResultDto> findAdminBillingShifts(Long agencyId, PageRequest of) {
        List<Shift> agentList ;
        agentList = shiftRepository.findAllByAgencyIdAndStatusAndIsAdminBilled(agencyId, "AUTHORIZED", false);
        Page<Shift> page = PaginationUtil.paginateShift(of, agentList);
        return page.map(toShiftResultDto::convert);

    }
    @Override
    @Transactional
    public Page<BookingResultDto> findAdminBillingShifts(Long agencyId, LocalDate startDate, LocalDate endDate, PageRequest of) {
        List<Shift> agentList ;
        agentList = shiftRepository.findAllByAgencyIdAndStatusAndIsAdminBilled(agencyId, "AUTHORIZED", false);

        List<Shift> val = agentList.stream()
                .filter(shift -> checkAfter(shift.getStart().toString(), startDate))
                .filter(shift -> checkBefore(shift.getStart().toString(), endDate))
                .collect(Collectors.toList());
        Page<Shift> page = PaginationUtil.paginateShift(of, val);

        return page.map(toShiftResultDto::convert);

    }

    @Override
    public List<AgencyResultDto> findByIsTransporter(boolean isTransporter) {
        List<Agency> agencyList = agencyRepository.findByIsTransporter(isTransporter);

        return agencyList.stream()
                .map(toAgencyResultDto::convert)
                .collect(Collectors.toList());
    }

    @Override
    public Page<WorkerResultDto> findAllApplicantsByCode(Long agencyId, String assignmentCode, PageRequest of) {

        List<WorkerResultDto> agentList = new ArrayList<>();
        Page<Worker> agentList2= null;

        agentList2 = workerRepository.findApplicantForAnAgency(agencyId, of);
        agentList2.forEach(a-> a.setActiveAgency(agencyId));

        List<WorkerResultDto> vfef = agentList2.stream()
                .filter(w -> (w.getStatus() == null || w.getStatus() == WorkerStatus.APPLICANT) && w.getAssignmentCode().toString().equalsIgnoreCase(assignmentCode))
                .map(toWorkerResultDto::convert)
                .collect(Collectors.toList());


        Page<WorkerResultDto> pagedWorkers = PaginationUtil.paginateIWorker(of, vfef);
        return pagedWorkers;
    }

    @Override
    public List<Agency> getDeputyAgencies() {
        return agencyRepository.findAllByDeputyEnabled(true);
    }

    private boolean checkAfter(String leftDate, LocalDate rightDate) {
        return convertFromString(leftDate).compareTo(rightDate) >= 0;
    }

    private boolean checkBefore(String leftDate, LocalDate rightDate) {
        return convertFromString(leftDate).compareTo(rightDate) <= 0;
    }

    private LocalDate convertFromString(String aDate) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            return LocalDate.parse(aDate, formatter);
        } catch (DateTimeParseException e) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
            return LocalDate.parse(aDate, formatter);
        }
    }

}

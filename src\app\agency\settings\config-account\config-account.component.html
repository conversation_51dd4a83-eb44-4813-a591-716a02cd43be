<div class="">
  <div class="">
    <div class="">
      <div class="">
        <div class="mt-4"></div>
        <div>
          <div class="card text-left">
            <div class="card-body">
              <div class="row">
                <!-- Left Column: Upload Logo and Choose File -->
                <div class="col-md-6">
                  <div class="mt-4">Upload Logo</div>
                  <div class="mt-4">
                    <div
                      class="text-center bg-main my-2"
                      style="max-width: 150px"
                    >
                      <img
                        class="card-img-top w-75 my-3"
                        [src]="agency?.logo"
                        alt="worker pic"
                        onError="this.src='assets/images/Placeholder_500x500.jpg'"
                      />
                    </div>
                    <input
                      type="file"
                      id="file"
                      (change)="onChange($event)"
                      accept="image/png, image/webp, image/svg, image/jpg, image/jpeg"
                    />
                    <input type="hidden" [ngModel]="agencyId" />
                  </div>
                </div>

                <!-- Right Column: Business Address and Phone Number -->
                <!-- <form
                  [formGroup]="bankForm"
                  (ngSubmit)="createSettings(bankForm)"
                >
                  <div class="col-12">
                    <div class="mt-4">
                      <input
                        type="text"
                        class="form-control"
                        formControlName="businessAddress"
                        placeholder="Enter Business Address"
                      />
                    </div>

                    <div class="mt-4"> -->
                      <!-- <input type="text" class="form-control" formControlName="phoneNumber" placeholder="Enter Phone Number"> -->
                      <!-- <phone-input
                        formControlName="phoneNumber"
                        [defaultCountry]="'ZW'"
                        (validationStateChange)="
                          onPhoneValidationChange($event)
                        "
                      />
                      <div
                        class="invalid-feedback d-block"
                        *ngIf="
                          bankForm.get('phoneNumber')?.touched &&
                          bankForm.get('phoneNumber')?.errors?.['required']
                        "
                      > -->
                        <!-- Phone number is required
                      </div>
                      <div
                        class="invalid-feedback d-block"
                        *ngIf="
                          phoneValidationError &&
                          bankForm.get('phoneNumber')?.touched
                        "
                      >
                        {{ phoneValidationError }}
                      </div>
                    </div>
                  </div>
                </form>
              </div> -->

              <div class="card-body">
              <div class="row">
                <div class="col-md-6" style="margin-top: 75px;">
                  <p class="mb-1"><strong>Company or Trade Name:</strong> {{ selectedagency?.name }}</p>
                  <p class="mb-0"><strong>Email:</strong> {{ selectedagency?.email }}</p>
                  <p class="mb-0"><strong>Phone Number:</strong> {{ selectedagency?.telephone }}</p>
                  <p class="mb-0"><strong>Base Currency:</strong> {{ selectedagency?.baseCurrency || 'USD' }}</p>
                </div>
              </div>
            <!-- </div> -->

              <!-- Save Settings Button -->
              <button
                *ngIf="!loading && 'UPDATE_SETTINGS' | permission"
                (click)="uploadLogo()"
                class="btn btn-success mt-4"
              >
                Save Settings
              </button>
            </div>
          </div>

          <!-- Loading Spinner -->
          <div class="card text-left">
            <div
              class="spinner-grow mr-3"
              *ngIf="loading"
              role="status"
              style="width: 1rem; height: 1rem"
            >
              <span class="sr-only">Loading...</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

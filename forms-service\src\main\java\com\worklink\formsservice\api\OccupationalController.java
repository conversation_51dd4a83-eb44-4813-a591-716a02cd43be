package com.worklink.formsservice.api;


import com.worklink.formsservice.dto.education.EducationsCreateDto;
import com.worklink.formsservice.dto.education.IEducationResultDto;
import com.worklink.formsservice.dto.employer.EmployersCreateDto;
import com.worklink.formsservice.dto.employer.IEmployerResultDto;
import com.worklink.formsservice.dto.employment.EmploymentsCreateDto;
import com.worklink.formsservice.dto.employment.IEmploymentResultDto;
import com.worklink.formsservice.dto.reference.IReferenceResultDto;
import com.worklink.formsservice.dto.reference.ReferencesCreateDto;
import com.worklink.formsservice.dto.occupational.OccupationalCreateDto;
import com.worklink.formsservice.dto.occupational.OccupationalResultDto;
import com.worklink.formsservice.model.Hmrc;
import com.worklink.formsservice.model.Occupational;
import com.worklink.formsservice.service.OccupationalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.net.URI;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class OccupationalController {


    private final OccupationalService occupationalService;

    public OccupationalController(OccupationalService occupationalService) {
        this.occupationalService = occupationalService;
    }

    @PostMapping(value = "occupational", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<OccupationalCreateDto> createOccupational(@RequestBody OccupationalCreateDto occupationalCreateDto) {
        log.info("Request to add occupational with : {}", occupationalCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        occupationalService.addOccupational(occupationalCreateDto);
        return ResponseEntity.ok( occupationalService.addOccupational(occupationalCreateDto));
    }

    @PutMapping(value = "occupational", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Occupational> responseWorkerTraining(@RequestBody OccupationalCreateDto occupational) {
        log.info("Request to update workerTraining with : {}", occupational);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        occupationalService.updateWorkerOccupational(occupational);
        return ResponseEntity.created(uri)
                .build();
    }



    /*@ViewServices*/
    @GetMapping(value = "occupational/{workerId}")
    public ResponseEntity<OccupationalResultDto> findOccupationals
    (
            @PathVariable("workerId") Long workerId
    ){
        log.info("Request to get worker  occupational for worker_id: {}", workerId);
        return ResponseEntity.ok(occupationalService.findOccupationals(workerId));
    }

    @GetMapping(value = "occupational/worker/{id}")
    public ResponseEntity<Occupational> findByWorkerId(@PathVariable("id") Long id) {
        log.info("Request to get occupational with id : {}", id);
        return ResponseEntity.ok(occupationalService.findByWorkerId(id));
    }


    @GetMapping(value = "occupational/{workerId}/{agencyId}")
    public ResponseEntity<OccupationalResultDto> findAgencyOccupationals
            (
                    @PathVariable("workerId") Long workerId,
                    @PathVariable("agencyId") Long agencyId
            ){
        log.info("Request to get worker  workerTraining for worker_id: {}", workerId);
        return ResponseEntity.ok(occupationalService.findAgencyWorkerOccupational(workerId,agencyId));
    }


    @DeleteMapping(value = "occupational/{id}")
    public ResponseEntity deleteOccupational(@PathVariable("id") Long id) {
        occupationalService.deleteOccupational(id);
        return ResponseEntity.noContent().build();
    }


    @PostMapping(value = "occupational/signature-upload",consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity uploadSignature(@RequestParam("file") MultipartFile file,
                                          @RequestParam("workerId") Long workerId
    ){

        log.info("Request to add worker profile image : {}");
        occupationalService.addSignature( workerId, file);
        return  ResponseEntity.noContent().build();
    }@PostMapping(value = "occupational/varicella-upload",consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity addVaricella(@RequestParam("file") MultipartFile file,
                                          @RequestParam("workerId") Long workerId
    ){

        log.info("Request to add varicella : {}");
        occupationalService.addVaricella( workerId, file);
        return  ResponseEntity.noContent().build();
    }@PostMapping(value = "occupational/tb-upload",consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity addTb(@RequestParam("file") MultipartFile file,
                                          @RequestParam("workerId") Long workerId
    ){

        log.info("Request to add tb : {}");
        occupationalService.addTb( workerId, file);
        return  ResponseEntity.noContent().build();
    }

    @PostMapping(value = "occupational/mmr-upload",consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity addMmr(@RequestParam("file") MultipartFile file,
                                          @RequestParam("workerId") Long workerId
    ){

        log.info("Request to add worker profile image : {}");
        occupationalService.addMmr( workerId, file);
        return  ResponseEntity.noContent().build();
    }

    @PostMapping(value = "occupational/hepb-upload",consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity addHepb(@RequestParam("file") MultipartFile file,
                                          @RequestParam("workerId") Long workerId
    ){

        log.info("Request to add hepb : {}");
        occupationalService.addHepb( workerId, file);
        return  ResponseEntity.noContent().build();
    }

    @PostMapping(value = "occupational/hepbs-upload",consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity addHepbs(@RequestParam("file") MultipartFile file,
                                          @RequestParam("workerId") Long workerId
    ){

        log.info("Request to add hepbs : {}");
        occupationalService.addHepbs( workerId, file);
        return  ResponseEntity.noContent().build();
    }

    @PostMapping(value = "occupational/hepc-upload",consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity addHepc(@RequestParam("file") MultipartFile file,
                                          @RequestParam("workerId") Long workerId
    ){

        log.info("Request to add hepc : {}");
        occupationalService.addHepc( workerId, file);
        return  ResponseEntity.noContent().build();
    }
    @PostMapping(value = "occupational/hiv-upload",consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity<Object> uploadHiv(@RequestParam("file") MultipartFile file, @RequestParam("workerId") Long workerId){
        log.info("Request to add hiv for worker : {}",workerId);
        occupationalService.addHiv( workerId, file);
        return  ResponseEntity.noContent().build();
    }

}

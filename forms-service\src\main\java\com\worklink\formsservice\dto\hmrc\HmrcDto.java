package com.worklink.formsservice.dto.hmrc;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;

@Data
public class HmrcDto {

    private Long id;
    private String lastname;
    private String firstname;
    private String lastModifiedDate;
    private String address;
    private String insuarance;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate employmentStart;
    private String sex;
    private String dob;
    private Boolean otherJob;
    private Boolean payments;
    private Boolean april;
    private String statement;
    private Boolean loan;
    private Boolean applyStatement;
    private String plan;
    private String signed;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "Africa/Harare")
    private LocalDate signDate;
    private String fullname;
    private String submitted;
    private Long workerId;
    private Long agencyId;
    private String comment;
    private String status;
}

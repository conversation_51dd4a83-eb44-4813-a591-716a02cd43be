package com.worklink.formsservice.dto.occupational;

import java.time.LocalDate;

public interface IOccupationalResultDto {

    Long getId();
    String getTitle();
    String getLastname();
    String getFirstname();
    String getPreferredName();
    String getLastModifiedDate();

    String getNationality();
    LocalDate getDob();
    String getAddress();
    String getEmail();
    String getPhone();
    Boolean getDisability();
    String getDisabilityRes();
    Boolean getDisabilityByWork();
    String getDisabilityByWorkRes();
    Boolean getCurrentTreatment();
    String getCurrentTreatmentRes();
    Boolean getAssistance();
    String getAssistanceRes();
    Boolean getMrsa();
    LocalDate getMrsaDate();
    Boolean getCdiff();
    LocalDate getCdiffDate();
    Boolean getChickenpox();
    LocalDate getChickenpoxDate();
    Boolean getBbv();
    LocalDate getBbvDate();
    Boolean getTravelled();
    Boolean getBcg();
    LocalDate getBcgDate();
    Boolean getCough();
    Boolean getWeight();
    Boolean getFever();
    Boolean getTb();
    String getTbcRes();
    Boolean getTripleVac();
    LocalDate getTripleVacDate();
    Boolean getPolio();
    LocalDate getPolioDate();
    Boolean getTetanus();
    LocalDate getTetanusDate();
    Boolean getHepb();
    LocalDate getC1();
    LocalDate getC2();
    LocalDate getC3();
    LocalDate getB1();
    LocalDate getB2();
    LocalDate getB3();
    Boolean getVarUp();
    Boolean getTbUp();
    Boolean getRubUp();
    Boolean getHepbUP();
    Boolean getHepbsUp();
    Boolean getHepcUp();
    Boolean getHivUp();
    Boolean getExposure();
    Boolean getConsent();
    Boolean getCopy();
    String getFullname();
    Boolean getSigUp();
    LocalDate getSigDate();
    Long getWorkerId();
}

spring:
  sleuth:
    sampler:
      probability: 1
server:
  port: 8765
eureka:
  instance:
    leaseRenewalIntervalInSeconds: 1
    leaseExpirationDurationInSeconds: 2
    prefer-ip-address: true
  client:
    registerWithEureka: true
    fetchRegistry: true
    healthcheck:
      enabled: true
    lease:
      duration: 5
    serviceUrl:
      defaultZone: http://*************:8761/eureka/
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 30000
package com.worklink.formsservice.dao;

import com.worklink.formsservice.model.AgencyWorkerOccupational;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface AgencyWorkerOccupationalRepository extends JpaRepository<AgencyWorkerOccupational, Long> {

    AgencyWorkerOccupational findByWorkerIdAndAgencyId(Long workerId, Long agencyId);

}
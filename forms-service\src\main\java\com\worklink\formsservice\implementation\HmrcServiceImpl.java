package com.worklink.formsservice.implementation;

import com.worklink.formsservice.dao.*;
import com.worklink.formsservice.dto.file.FileDto;
import com.worklink.formsservice.dto.hmrc.HmrcDto;
import com.worklink.formsservice.dto.occupational.OccupationalResultDto;
import com.worklink.formsservice.enums.FormStatus;
import com.worklink.formsservice.exception.BusinessValidationException;
import com.worklink.formsservice.exception.RecordNotFoundException;
import com.worklink.formsservice.helpers.DataBucketUtil;
import com.worklink.formsservice.model.*;
import com.worklink.formsservice.service.HmrcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static java.time.LocalDateTime.now;

@Service
@Slf4j
public class HmrcServiceImpl implements HmrcService {
    @Value("${storage.volume.path}")
    private  String rootPath;


//    private final HmrcToHmrcDto toHmrcDto;

    private final HmrcRepository hmrcRepository;
    private final AgencyWorkerHmrcRepository agencyWorkerHmrcRepository;

    private final EmployerRepository employerRepository;
    private final EducationRepository educationRepository;

    private final DataBucketUtil dataBucketUtil;
    private final ReferenceRepository referenceRepository;
    private final EmploymentRepository employmentRepository;

    public HmrcServiceImpl(HmrcRepository hmrcRepository,
                           AgencyWorkerOccupationalRepository agencyWorkerOccupationalRepository,
                           AgencyWorkerHmrcRepository agencyWorkerHmrcRepository,
                           EmployerRepository employerRepository,
                           EducationRepository educationRepository,
                           DataBucketUtil dataBucketUtil,
                           ReferenceRepository referenceRepository,
                           EmploymentRepository employmentRepository) {
        this.hmrcRepository = hmrcRepository;
        this.agencyWorkerHmrcRepository = agencyWorkerHmrcRepository;
        this.employerRepository = employerRepository;
        this.educationRepository = educationRepository;
        this.dataBucketUtil = dataBucketUtil;
        this.referenceRepository = referenceRepository;
        this.employmentRepository = employmentRepository;
    }


    @Override
    public HmrcDto addHmrc(HmrcDto hmrcCreateDto) {
        Hmrc hmrc = new Hmrc();
        Hmrc hmrc1 = hmrcRepository.findByWorkerId(hmrcCreateDto.getWorkerId());

        if(hmrc1!=null) {
            hmrc =hmrc1;
        }
        hmrc.setId(hmrcCreateDto.getId());
        hmrc.setLastname(hmrcCreateDto.getLastname());
        hmrc.setFirstname(hmrcCreateDto.getFirstname());
        hmrc.setAddress(hmrcCreateDto.getAddress());
        hmrc.setInsuarance(hmrcCreateDto.getInsuarance());
        hmrc.setEmploymentStart(hmrcCreateDto.getEmploymentStart());
        hmrc.setSex(hmrcCreateDto.getSex());
        hmrc.setDob(hmrcCreateDto.getDob());
        hmrc.setOtherJob(hmrcCreateDto.getOtherJob());
        hmrc.setPayments(hmrcCreateDto.getPayments());
        hmrc.setApril(hmrcCreateDto.getApril());
        hmrc.setStatement(hmrcCreateDto.getStatement());
        hmrc.setLoan(hmrcCreateDto.getLoan());
        hmrc.setApplyStatement(hmrcCreateDto.getApplyStatement());
        hmrc.setPlan(hmrcCreateDto.getPlan());
        hmrc.setSigned(hmrcCreateDto.getSigned());
        hmrc.setSignDate(hmrcCreateDto.getSignDate());
        hmrc.setFullname(hmrcCreateDto.getFullname());
        hmrc.setLastModifiedDate(now());
        hmrc.setSubmitted(hmrcCreateDto.getSubmitted());
        hmrc.setWorkerId(hmrcCreateDto.getWorkerId());

        hmrcRepository.saveAndFlush(hmrc);

        return hmrcCreateDto;
    }



    @Override
    public void updateHmrc(HmrcDto application) {
        AgencyWorkerHmrc agencyWorkerApplication;

        agencyWorkerApplication = agencyWorkerHmrcRepository
                .findByWorkerIdAndAgencyId(
                        application.getWorkerId(),
                        application.getAgencyId()
                );

        if (agencyWorkerApplication   ==  null){

            agencyWorkerApplication = new AgencyWorkerHmrc();
            agencyWorkerApplication.setAgencyId(application.getAgencyId());
            agencyWorkerApplication.setWorkerId(application.getWorkerId());

        }


        log.info("Updated worker-application{}", agencyWorkerApplication);


        agencyWorkerApplication.setComment(application.getComment());

        if (application.getStatus().equalsIgnoreCase(FormStatus.REJECTED.toString())){
            agencyWorkerApplication.setStatus(FormStatus.REJECTED);
        }

        if (application.getStatus().equalsIgnoreCase(FormStatus.APPROVED.toString())){
            agencyWorkerApplication.setStatus(FormStatus.APPROVED);
        }

        agencyWorkerHmrcRepository.save(agencyWorkerApplication);


    }



    @Override
    public void addApplicationDoc(Long workerId,Long compliance, MultipartFile file) {
        log.info("Request to upload a payslip");
        String name = "application-"+compliance+".pdf";

        Hmrc hmrc = getOne(compliance);


        List<String> types = new ArrayList<String>();

        types.add("application/pdf");
//        types.add("image/jpeg");
//        types.add("image/jpg");


        if (!types.contains(file.getContentType())) {
            throw new BusinessValidationException("Uploaded file type is not supported. Please upload a pdf");
        }

        if (!file.isEmpty() ) {
            try {
                byte[] bytes = file.getBytes();

                File dir = new File(rootPath + File.separator + "worker"+ File.separator + workerId  );

                if (!dir.exists())  dir.mkdirs();

                // Create the file on server
                File serverFile = new File(dir.getAbsolutePath()
                        + File.separator + name);
                BufferedOutputStream stream = new BufferedOutputStream(
                        new FileOutputStream(serverFile));
                stream.write(bytes);
                stream.close();


                log.info("Server File Location="
                        + serverFile.getAbsolutePath());

                log.info("Uploaded");
//                hmrc.setUploaded(true);
                hmrcRepository.save(hmrc);

            } catch (Exception e) {
                log.info("You failed to upload");
                log.error(e.getMessage());
                throw new BusinessValidationException("Upload failed.");

            }

        } else {
            log.info("You failed to upload");
            throw new BusinessValidationException("Uploaded file is not empty.");

        }
    }

    @Override
    public void deleteHmrc(Long id) {

        Hmrc availability = hmrcRepository.findById(id).get();

        hmrcRepository.delete(availability);
        hmrcRepository.flush();


    }



    @Override
    public HmrcDto findHmrcs(Long workerId) {

        Hmrc hmrc = hmrcRepository.findByWorkerId(workerId);
        if(hmrc==null){
            return null;
        }

        DateTimeFormatter pattern = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        log.info("Found worker occupational: {}", hmrc.getLastModifiedDate());
        if(hmrc.getLastModifiedDate()!=null)hmrc.setLastModifiedBy(hmrc.getLastModifiedDate().format(pattern));


        return convert(hmrc);
    }

    private HmrcDto convert(Hmrc hmrc) {

        HmrcDto hmrcResultDto = new HmrcDto();


        hmrcResultDto.setId(hmrc.getId());
        hmrcResultDto.setId(hmrc.getId());
        hmrcResultDto.setLastname(hmrc.getLastname());
        hmrcResultDto.setFirstname(hmrc.getFirstname());
        hmrcResultDto.setAddress(hmrc.getAddress());
        hmrcResultDto.setInsuarance(hmrc.getInsuarance());
        hmrcResultDto.setEmploymentStart(hmrc.getEmploymentStart());
        hmrcResultDto.setSex(hmrc.getSex());
        hmrcResultDto.setDob(hmrc.getDob());
        hmrcResultDto.setOtherJob(hmrc.getOtherJob());
        hmrcResultDto.setPayments(hmrc.getPayments());
        hmrcResultDto.setApril(hmrc.getApril());
        hmrcResultDto.setStatement(hmrc.getStatement());
        hmrcResultDto.setLoan(hmrc.getLoan());
        hmrcResultDto.setApplyStatement(hmrc.getApplyStatement());
        hmrcResultDto.setPlan(hmrc.getPlan());
        hmrcResultDto.setSignDate(hmrc.getSignDate());
        hmrcResultDto.setFullname(hmrc.getFullname());
        hmrcResultDto.setSubmitted(hmrc.getSubmitted());
        hmrcResultDto.setLastModifiedDate(hmrc.getLastModifiedBy());
        hmrcResultDto.setWorkerId(hmrc.getWorkerId());



        return hmrcResultDto;
    }


    @Override
    public Hmrc findByWorkerId(Long id) {
        return hmrcRepository.findByWorkerId(id);
    }

    @Override
    public HmrcDto findAgencyWorkerHmrcs(Long workerId, Long agencyId) {

        Hmrc res = hmrcRepository.findByWorkerId(workerId);

        if(res==null){
            return null;
        }
        DateTimeFormatter pattern = DateTimeFormatter.ofPattern("dd/MM/yyyy");
//        log.info("Found worker occupational: {}", res.getLastModifiedDate());
        if(res.getLastModifiedDate()!=null)res.setLastModifiedBy(res.getLastModifiedDate().format(pattern));

        HmrcDto hmrcDto = convert(res);

        AgencyWorkerHmrc app;
        app = agencyWorkerHmrcRepository.findByWorkerIdAndAgencyId(workerId, agencyId);

        if(app== null) {
            app = new AgencyWorkerHmrc();
        }


        if(app.getStatus()!=null) hmrcDto.setStatus(app.getStatus().toString());
        if(app.getAgencyId()!=null) hmrcDto.setAgencyId(app.getAgencyId());
        if(app.getComment()!=null) hmrcDto.setComment(app.getComment());

        return hmrcDto;
    }



    @Override
    public Page<Hmrc> findAllPaged(PageRequest of) {
        return null;
    }

    @Override
    public Hmrc save(HmrcDto hmrcUpdateDto) {
        return null;
    }

    @Override
    public Hmrc getOne(Long id) {
        return hmrcRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Worker application not found"));
    }


    @Override
    public void addSignature(Long workerId, MultipartFile files){


        log.info("Start file uploading service");
//        List<Bank> inputFiles = new ArrayList<>();

        Arrays.asList(files).forEach(file -> {
            String originalFileName = file.getOriginalFilename();
            if(originalFileName == null){
                throw new BusinessValidationException("Original file city is null");
            }
            Path path = new File(originalFileName).toPath();

//            try {
            String contentType = null;
            try {
                contentType = Files.probeContentType(path);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            FileDto fileDto = dataBucketUtil.uploadFile(file, "w/f/"+ workerId+"/"+originalFileName, contentType);

            if (fileDto != null) {
                Hmrc hmrc = hmrcRepository.findByWorkerId(workerId);

                if(hmrc==null){
                    hmrc = new Hmrc();
                    hmrc.setWorkerId(workerId);
                }


                hmrc.setSigned(fileDto.getFileUrl());

                hmrcRepository.save(hmrc);
//                    inputFiles.add(new InputFile(fileDto.getFileName(), fileDto.getFileUrl()));
                log.debug("File uploaded successfully, file city: {} and url: {}",fileDto.getFileName(), fileDto.getFileUrl() );
            }
//            } catch (Exception e) {
//                log.error("Error occurred while uploading. Error: ", e);
//
//                    throw new FileNotFoundException("Error occurred while uploading");
//
//            }
        });

//        fileRepository.saveAll(inputFiles);
        log.debug("File details successfully saved in the database");





    }



}

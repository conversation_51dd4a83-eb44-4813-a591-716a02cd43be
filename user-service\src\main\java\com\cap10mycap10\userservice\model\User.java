package com.cap10mycap10.userservice.model;

import com.cap10mycap10.userservice.dto.UserDto;
import com.cap10mycap10.userservice.enums.UserType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "users")
@Data
public class User extends AbstractAuditingEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String firstName;

    private String lastName;

    @Column(name = "username")
    private String username;

    @Column(name = "password")
    private String password;

    @Column(name = "email")
    private String email;

    @Column(name = "otp")
    private String otp;

    @Column(name = "otp_expiry")
    private LocalDateTime otpExpiry;

    @Column(name = "agent_id")
    private Long agentId;

    @Column(name = "client_id")
    private Long clientId;

    @Column(name = "last_password")
    private String lastPassword;

    @Column(name = "worker_id")
    private Long workerId;

    @Column(name = "enabled")
    private boolean enabled;

    @Column(name = "last_password_change")
    private LocalDate lastPasswordChange;

    @Column(name = "user_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private UserType userType =  UserType.WORKER;

    @Column(name = "preferred_currency", length = 3)
    private String preferredCurrency = "USD"; // Default to USD, ISO 4217 currency code

    @ManyToMany(fetch = FetchType.EAGER)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JoinTable(name = "role_user", joinColumns = {@JoinColumn(name = "user_id", referencedColumnName = "id")},
            inverseJoinColumns = {
                    @JoinColumn(name = "role_id", referencedColumnName = "id")})
    private List<Role> roles;

    public User() {
    }

    public User(User user) {
        this.username = user.getUsername();
        this.password = user.getPassword();
        this.email = user.getEmail();
        this.enabled = user.isEnabled();
        this.roles = user.getRoles();
    }

    public void addRole(Role role) {
        if (roles == null) {
            roles = new ArrayList<>();
        }
        roles.add(role);
    }

    public List<Role> getRoles() {
        if (roles == null) {
            roles = new ArrayList<>();
        }
        return roles;
    }


    public void updateFields(UserDto accountDto) {
        this.setFirstName(accountDto.getFirstName());
        this.setLastName(accountDto.getLastName());
        this.setUsername(accountDto.getUsername());
        this.setUserType(accountDto.getUserType());
        if ((accountDto.getAgentId() != null) && (accountDto.getAgentId() > 0)) {
            this.setAgentId(accountDto.getAgentId());
        }
        if ((accountDto.getClientId() != null) && (accountDto.getClientId() > 0)) {
            this.setClientId(accountDto.getClientId());

        }
        if ((accountDto.getWorkerId() != null) && (accountDto.getWorkerId() > 0)) {
            this.setWorkerId(accountDto.getWorkerId());

        }
    }
}
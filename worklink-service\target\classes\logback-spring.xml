<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <appender name="dailyRollingFile"  class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>./worklink-service/logs/worklink.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- daily rollover -->
            <fileNamePattern>./worklink-service/logs/worklink.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxHistory>30</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>20MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>%-5level - %replace(%msg){'\d{12,19}', 'XXXX'}%n</pattern>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{35} - %msg %n</pattern>
            <!--<encoder class="net.logstash.logback.encoder.LogstashEncoder"/>-->
        </encoder>
    </appender>

    <appender name="severDailyRollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>./worklink-service/logs/severe.log</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- daily rollover -->
            <fileNamePattern>./worklink-service/logs/severe-sms.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxHistory>30</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>20MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LogstashEncoder"/>
    </appender>

    <appender name="consoleRolling" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d %p %C{1.} [%t] %m%n</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>


    <logger name="ng.akupay.smsservice" level="INFO"
            additivity="false">
        <appender-ref ref="dailyRollingFile"/>
        <appender-ref ref="consoleRolling"/>
        <appender-ref ref="severDailyRollingFile"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="dailyRollingFile"/>
        <appender-ref ref="consoleRolling"/>
        <appender-ref ref="severDailyRollingFile"/>
    </root>


</configuration>



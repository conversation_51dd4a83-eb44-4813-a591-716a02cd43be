"use client";

import React from "react";
import { Currency } from "@/common/models";
import { formatCurrency, getCurrencySymbol } from "@/common/lib/currency-utils";

interface CurrencySelectorProps {
  currencies: Currency[];
  selectedCurrency?: string;
  onCurrencyChange: (currencyCode: string) => void;
  label?: string;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  showSymbol?: boolean;
  required?: boolean;
}

/**
 * Currency selector component for choosing from available currencies
 */
export default function CurrencySelector({
  currencies,
  selectedCurrency,
  onCurrencyChange,
  label = "Currency",
  placeholder = "Select currency",
  disabled = false,
  className = "",
  showSymbol = true,
  required = false,
}: CurrencySelectorProps) {
  const activeCurrencies = currencies.filter((currency) => currency.active);

  const handleChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    onCurrencyChange(event.target.value);
  };

  return (
    <div className={`flex flex-col gap-2 ${className}`}>
      {label && (
        <label className="text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      <select
        value={selectedCurrency || ""}
        onChange={handleChange}
        disabled={disabled}
        required={required}
        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
      >
        <option value="" disabled>
          {placeholder}
        </option>
        {activeCurrencies.map((currency) => (
          <option key={currency.code} value={currency.code}>
            {currency.code} - {currency.name}
            {showSymbol && ` (${currency.symbol || getCurrencySymbol(currency.code)})`}
          </option>
        ))}
      </select>
    </div>
  );
}

/**
 * Props for the currency display component
 */
interface CurrencyDisplayProps {
  amount: number;
  currencyCode: string;
  className?: string;
  showCode?: boolean;
  locale?: string;
}

/**
 * Component to display formatted currency amounts
 */
export function CurrencyDisplay({
  amount,
  currencyCode,
  className = "",
  showCode = false,
  locale = "en-US",
}: CurrencyDisplayProps) {
  const formattedAmount = formatCurrency(amount, currencyCode, locale);

  return (
    <span className={className}>
      {formattedAmount}
      {showCode && ` ${currencyCode}`}
    </span>
  );
}

/**
 * Props for the currency conversion display component
 */
interface CurrencyConversionDisplayProps {
  originalAmount: number;
  originalCurrency: string;
  convertedAmount: number;
  convertedCurrency: string;
  exchangeRate: number;
  className?: string;
  showExchangeRate?: boolean;
  locale?: string;
}

/**
 * Component to display currency conversion with original and converted amounts
 */
export function CurrencyConversionDisplay({
  originalAmount,
  originalCurrency,
  convertedAmount,
  convertedCurrency,
  exchangeRate,
  className = "",
  showExchangeRate = false,
  locale = "en-US",
}: CurrencyConversionDisplayProps) {
  const originalFormatted = formatCurrency(originalAmount, originalCurrency, locale);
  const convertedFormatted = formatCurrency(convertedAmount, convertedCurrency, locale);

  return (
    <div className={`flex flex-col gap-1 ${className}`}>
      <div className="flex items-center justify-between">
        <span className="text-sm text-gray-600">Original:</span>
        <span className="font-medium">{originalFormatted}</span>
      </div>
      <div className="flex items-center justify-between">
        <span className="text-sm text-gray-600">Converted:</span>
        <span className="font-semibold text-blue-600">{convertedFormatted}</span>
      </div>
      {showExchangeRate && (
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>Exchange Rate:</span>
          <span>1 {originalCurrency} = {exchangeRate.toFixed(4)} {convertedCurrency}</span>
        </div>
      )}
    </div>
  );
}

/**
 * Common currency codes for quick selection
 */
export const COMMON_CURRENCIES = [
  "USD", "EUR", "GBP", "CAD", "AUD", "JPY", "CHF", "CNY", "SEK", "NOK"
];

/**
 * Props for the common currency selector
 */
interface CommonCurrencySelectorProps {
  selectedCurrency?: string;
  onCurrencyChange: (currencyCode: string) => void;
  className?: string;
  disabled?: boolean;
}

/**
 * Quick selector for common currencies
 */
export function CommonCurrencySelector({
  selectedCurrency,
  onCurrencyChange,
  className = "",
  disabled = false,
}: CommonCurrencySelectorProps) {
  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {COMMON_CURRENCIES.map((currency) => (
        <button
          key={currency}
          type="button"
          onClick={() => onCurrencyChange(currency)}
          disabled={disabled}
          className={`px-3 py-1 text-sm border rounded-md transition-colors ${
            selectedCurrency === currency
              ? "bg-blue-500 text-white border-blue-500"
              : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
          } disabled:opacity-50 disabled:cursor-not-allowed`}
        >
          {currency}
        </button>
      ))}
    </div>
  );
}

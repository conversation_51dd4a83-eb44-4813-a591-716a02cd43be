package com.cap10mycap10.worklinkservice.service;


import com.cap10mycap10.worklinkservice.dto.agency.AgencyCreateDto;
import com.cap10mycap10.worklinkservice.dto.agency.AgencyResultDto;
import com.cap10mycap10.worklinkservice.dto.agency.AgencyStats;
import com.cap10mycap10.worklinkservice.dto.agency.AgencyUpdateDto;
import com.cap10mycap10.worklinkservice.dto.client.ClientDto;
import com.cap10mycap10.worklinkservice.dto.shift.BookingResultDto;
import com.cap10mycap10.worklinkservice.dto.worker.WorkerResultDto;
import com.cap10mycap10.worklinkservice.enums.AgencyType;
import com.cap10mycap10.worklinkservice.model.Agency;
import com.cap10mycap10.worklinkservice.model.BankDetails;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.util.List;

public interface AgencyService {


    void save(Agency agency);

    void save(AgencyCreateDto agencyCreateDto) throws JsonProcessingException;

    void save(AgencyUpdateDto agencyUpdateDto);
    void saveBankDetails(Long agencyId,BankDetails bankDetails);
    void updateBaseCurrency(Long agencyId, String baseCurrency);

    AgencyResultDto findById(Long id);

    List<AgencyResultDto> findAll();

    Page<AgencyResultDto> findAllPaged(AgencyType agencyTypeFilter,String searchQuery, PageRequest of);

    Page<AgencyResultDto> findAllDefaultPaged(PageRequest of);

    void deleteById(Long id);

    Agency getOne(Long id);

    Page<WorkerResultDto> findAllWorkersPaged(Long agencyId, PageRequest of);

    Page<WorkerResultDto> findAllApplicantsPaged(Long agencyId, PageRequest of);

    Page<WorkerResultDto> findAllWorkersPagedPending(Long agencyId, PageRequest of);

    Page<BookingResultDto> findAllShiftsAgencyPaged(Long agencyId, PageRequest of, LocalDate startDate, LocalDate endDate);

    List<AgencyResultDto> findAllShifts();

    List<AgencyResultDto> findTrainers();

    Integer findNumberOfAgencies();


    Page<BookingResultDto> findAllShiftsAgencyPaged(Long agencyId, PageRequest of, LocalDate startDate, LocalDate endDate, String status);

    Page<BookingResultDto> findAllShiftsAgencyPagedByStatus(Long agencyId, PageRequest of, String status, Long clientId, LocalDate startDate, LocalDate endDate);
    Page<BookingResultDto> findAllReleasedShift(Long agencyId, PageRequest of);

    AgencyStats getStats(Long id);

    BookingResultDto removeWorker(Long shiftId, Long workerId);

    void activate(Long id);

    void deactivate(Long id);

    void addLogo(Long agencyId, MultipartFile file);

    Page<BookingResultDto> findAdminBillingShifts(Long agencyId, PageRequest of);
    Page<BookingResultDto> findAdminBillingShifts(Long agencyId, LocalDate startDate, LocalDate endDate, PageRequest of);

    List<AgencyResultDto> findByIsTransporter(boolean isTransporter);
    Page<WorkerResultDto> findAllApplicantsByCode(Long agencyId, String assignmentCode, PageRequest of);

    List<Agency> getDeputyAgencies();
}

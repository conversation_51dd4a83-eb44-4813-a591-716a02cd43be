export interface Address {
    county?: string;
    firstLine?: string;
    postcode?: string;
    secondLine?: string;
    town?: string;
}

export interface AdministratorCreateDto {
    adminEmail: string;
    firstname: string;
    lastname: string;
}

export enum RatingStatus {
    CLEANLINESS = "CLEANLINESS",
    CONDITION = "CONDITION",
    COMFORT = "COMFORT",
    PROCESS = "PROCESS",
    PROFESSIONALISM = "PROFESSIONALISM",
}

export interface RatingItem {
    id?: number;
    rate: number;
    status: RatingStatus;
}

export interface Rating {
    comment: string;
    createdDate: string;
    lastModifiedDate: string;
    rating: number;
    name: string;
    ratingItems: RatingItem[];
    type: "CLIENT" | "VEHICLE"
}

export interface Client {
    address: Address;
    administratorCreateDto: AdministratorCreateDto;
    agencyId: number;
    billingEmail: string;
    email: string;
    id: number;
    logo: string;
    name: string;
    purchaseOrder: string;
    sbsCode: string;
    serviceId: number;
    telephone: string;
}

// This is the structure of the data returned from the
// OpenMaps location API
// TODO: Remove all usages of this since we no longer use OpenMaps
export interface LocationsSearchResult {
    place_id: number;
    display_name: string;
    lat: string;
    lon: string;
    name: string;
    type: string;
    importance: number;
    address: {
        city?: string;
        region?: string;
        state?: string;
        county?: string;
        country: string;
    };
}

export interface Location {
    adminName: string;
    capital: string;
    city: string;
    name: string;
    cityAscii: string;
    country: string;
    id: number;
    iso2: string;
    iso3: string;
    lat: number;
    lng: number;
}

export interface Client {
    address: Address;
    billingEmail: string;
    createdDate: string; // format: yyyy-MM-dd
    email: string;
    id: number;
    lastModifiedDate: string; // format: yyyy-MM-dd
    logo: string;
    name: string;
    purchaseOrder: string;
    sbsCode: string;
    status: "ACTIVE" | string;
    telephone: string;
    version: number;
    totalBooking: number;
    verified: boolean;
    rating: number | null;
    clientDocs: ClientDocument[];
}

export interface ClientDocument {
    createdDate: string;
    id: number;
    lastModifiedDate: string;
    name: "ID" | "PASSPORT" | "PROOF_RESIDENCE" | "DRIVER";
    status: "NEW" | string;
    url: string;
    version: number;
}

export interface PaymentRef {
    id: number;
    paymentDate: string;
    ref: string;
    status: string;
    total: number;
}

export interface InvoiceItem {
    assignmentCode: string;
    client: string;
    clientId: number;
    dayOfTheWeek: string;
    description: string;
    directorate: string;
    endDate: string;
    endTime: string;
    id: number;
    numberOfHoursWorked: number;
    rate: number;
    shiftId: number;
    shiftType: string;
    startDate: string;
    startTime: string;
    total: number;
    trainingId: number;
    worker: string;
}

export interface Invoice {
    agencyId: number;
    agency: Agency;
    agencyName: string;
    clientId: number;
    clientName: string;
    description: string;
    discount: number;
    dueDate: string;
    id: number;
    invoiceDate: string;
    invoiceItemResult: InvoiceItem[];
    invoiceStatus: string;
    invoiceType: "CLIENT" | string;
    payeeId: number;
    payments: PaymentRef[];
    published: boolean;
    subTotalAmount: number;
    totalAmount: number;
    vatAmount: number;
    vatPercentage: number;
    workerId: number;
    workerName: string;
    redirectUrl: string | null;
    clientSecret: string | null;
    serviceCharge?: number;
    serviceChargeDesc?: string;
}

export interface VehicleDocument {
    createdDate: string; // format: yyyy-MM-dd
    expiryDate: string; // format: yyyy-MM-dd
    id: number;
    lastModifiedDate: string; // format: yyyy-MM-dd
    name: string;
    status: "NEW" | string;
    url: string;
    version: number;
}

export interface VehicleInventory {
    createdDate: string; // format: yyyy-MM-dd
    dateInstalled: string; // format: yyyy-MM-dd
    description: string;
    id: number;
    lastModifiedDate: string; // format: yyyy-MM-dd
    name: string;
    nextCheckDate: string; // format: yyyy-MM-dd
    photoUrl1: string;
    photoUrl2: string;
    photoUrl3: string;
    photoUrl4: string;
    version: number;
    price: number;
    vehicleId: number;
}

export interface VehiclePhoto {
    createdDate: string; // format: yyyy-MM-dd
    id: number;
    lastModifiedDate: string; // format: yyyy-MM-dd
    url: string;
    version: number;
}

export interface VehicleBooking {
    byAgency: boolean;
    rating: Rating | null
    ratings: Rating[] | null
    cancelReason: string;
    client: Client;
    clientId: number;
    email: string;
    end: string; // format: yyyy-MM-dd'T'HH:mm
    firstname: string;
    id: number;
    invoices: Invoice[];
    //   location: string;
    phone: string;
    start: string; // format: yyyy-MM-dd'T'HH:mm
    status: "RESERVED" | "BOOKED" | "WAITINGAUTH" | "COMPLETE" | "CANCELLED";
    surname: string;
    vehicle: Vehicle;
    vehicleId: number;
    paymentRedirectUrl: string | null;
    clientSecret: string | null;
    promoCode?: string | null;
    promotion?: Promotion;
    verified?: boolean;
}

/**
 * Represents the daily rate for a vehicle
 */
export interface VehicleRate {
    id: number;
    createdDate: string;
    lastModifiedDate: string;
    rate: number;
    version: number;
    weekDay: "MTF" | "SUN" | "SAT";
}

/**
 * Represents a currency in the system
 */
export interface Currency {
    code: string; // ISO 4217 currency code (e.g., USD, GBP, EUR)
    name: string; // Full currency name (e.g., US Dollar, British Pound)
    symbol: string; // Currency symbol (e.g., $, £, €)
    active: boolean; // Whether this currency is active in the system
    decimalPlaces: number; // Number of decimal places for this currency
}

/**
 * Represents an exchange rate between two currencies
 */
export interface ExchangeRate {
    id: number;
    fromCurrency: string; // Source currency code
    toCurrency: string; // Target currency code
    rate: number; // Exchange rate from source to target
    rateDate: string; // When this rate was fetched (ISO 8601 format)
    createdAt: string; // When this record was created (ISO 8601 format)
    source: string; // Source of the exchange rate (STRIPE, MANUAL, etc.)
    isActive: boolean;
}
enum AgencyType {
    DEFAULT = "DEFAULT",
    TRAINER = "TRAINER",
    TRANSPORTER = "TRANSPORTER",
}

type AgencyBankDetails = {
    accountName: string;
    accountNumber: string;
    sortCode: string;
};

type AgencyServices = {
    agency: {
        createdDate: string; // ISO 8601 format
        id: number;
        lastModifiedDate: string; // yyyy-MM-dd format
        name: string;
        version: number;
    };
};

enum AgencyStatus {
    ACTIVE = "ACTIVE",
    INACTIVE = "INACTIVE",
    DELETED = "DELETED",
    APPLICANT = "APPLICANT",
    APPROVED = "APPROVED",
    WAITING = "WAITING",
    SUSPENDED = "SUSPENDED",
    REJECTED = "REJECTED",
    NEW = "NEW",
}

type Agency = {
    address: Address;
    agencyType: AgencyType;
    bankDetails: AgencyBankDetails;
    billingEmail: string;
    corrupted: boolean;
    createdDate: string; // ISO 8601 format
    deputyEnabled: boolean;
    deputyToken: string;
    deputyUrl: string;
    email: string;
    id: number;
    isTrainer: boolean;
    isTransporter: boolean;
    lastModifiedDate: string; // yyyy-MM-dd format
    logo: string;
    name: string;
    refPrefix: string;
    service: AgencyServices;
    status: AgencyStatus;
    telephone: string;
    transporter: boolean;
    version: number;
    baseCurrency?: string; // ISO 4217 currency code (e.g., USD, GBP, EUR)
    currency?: Currency;
};

export interface Vehicle {
    agency: Agency;
    airConditioning: boolean;
    approved: boolean;
    capacity: number;
    color: string;
    contactAddress: string;
    contactPerson: string;
    contactPhone: string;
    createdDate: string; // format: yyyy-MM-dd
    depositAmt: number;
    description: string;
    doors: number;
    engineNumber: string;
    engineSize: string;
    forRental: boolean;
    fuelType: "PETROL" | string;
    id: number;
    lastModifiedDate: string; // format: yyyy-MM-dd
    mainPhoto: string;
    maxAge: number;
    maxDailyMileage: number;
    excessMileageRate: number;
    mileage: number;
    minAge: number;
    minHireDays?: number;
    model: string;
    name: string;
    notes: string;
    regno: string;
    seats: number;
    status: "AVAILABLE" | string;
    stock: number;
    trackerId: string;
    transmissionType: "AUTO" | string;
    type: "SUV" | string;
    vehicleDocuments: VehicleDocument[];
    inventory: VehicleInventory[];
    vehicleRates: VehicleRate[];
    photos: VehiclePhoto[];
    version: number;
    rating: number | null;
    ratings: Rating[];
    location: Location;
    promotions: Promotion[]
}

export type Promotion = {
    /** Format: date-time */
    activatedDate?: string;
    adminDiscount?: boolean;
    /** Format: date-time */
    cancelledDate?: string;
    code?: string;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    createdDate?: string;
    /** Format: int32 */
    daysHired?: number;
    /** @example yyyy-MM-dd'T'HH:mm:ss'Z' */
    deletedAt?: string;
    description?: string;
    /** Format: float */
    discount?: number;
    /** Format: date-time */
    expiryDate?: string;
    /** Format: int32 */
    extraDays?: number;
    /** Format: int32 */
    extraMileage?: number;
    htmlBody?: string;
    /** Format: int64 */
    id?: number;
    /** @example yyyy-MM-dd */
    lastModifiedDate?: string;
    /** @enum {string} */
    promotionType?: PromotionType;
    /** Format: date-time */
    startDate?: string;
    /** @enum {string} */
    status?: PromotionStatus
    title?: string;
    /** Format: int32 */
    usageCount?: number;
    /** Format: int32 */
    usageLimit?: number;
    vehicles?: Vehicle[];
    /** Format: int64 */
    version?: number;
};

export type PromotionType = "ALL" | "DISCOUNT" | "EXTRA_MILEAGE" | "EXTRA_DAYS" | "OTHER_AWARD" | undefined

export type PromotionStatus = "ACTIVE" | "INACTIVE" | "APPROVED" | "WAITING" | "CANCELLED" | "REJECTED" | "NEW" | undefined

export interface Sort {
    empty: boolean;
    sorted: boolean;
    unsorted: boolean;
}

export interface Pageable {
    offset: number;
    pageNumber: number;
    pageSize: number;
    paged: boolean;
    unpaged: boolean;
    sort: Sort;
}

export interface PaginatedResponse<T> {
    content: T[];
    empty: boolean;
    first: boolean;
    last: boolean;
    number: number;
    numberOfElements: number;
    pageable: Pageable;
    size: number;
    sort: Sort;
    totalElements: number;
    totalPages: number;
}



version: '3'
services:

  traefik:
    image: traefik:alpine
    labels:
      - traefik.port=8080
      - traefik.enable=true

    restart: always

    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ${PWD}/traefik.toml:/etc/traefik/traefik.toml
      - ${PWD}/acme.json:/acme.json

    ports:
      - 80:80
      - 443:443
      - 8080:8080

    networks:
      net:
        ipv4_address: "*************"

  worklink-frontend:
    image: tinashemk/myrepository:worklink-frontend
    container_name: worklink-frontend
    labels:
      - traefik.frontend.rule=Host:test.myworklink.uk
      - traefik.enable=true
    networks:
      net:
        ipv4_address: "*************"



  oauth-service:
    image: tinashemk/myrepository:oauth-service
    environment:
      - "SPRING_PROFILES_ACTIVE=dev"
    volumes:
      - ouath_logs:/opt/ouath-service/logs
    container_name: oauth-service
    ports:
      - '8203:8203'
    restart: unless-stopped
    networks:
      net:
        ipv4_address: "*************"


  user-service:
    image: tinashemk/myrepository:user-service
    environment:
      - "SPRING_PROFILES_ACTIVE=dev"
    volumes:
      - user_logs:/opt/user-service/logs
    container_name: user-service
    ports:
      - '8204:8204'
    restart: unless-stopped
    networks:
      net:
        ipv4_address: "*************"


  eureka-service:
    image: tinashemk/myrepository:eureka-service
    container_name: eureka-service
    ports:
      - '8761:8761'
    labels:
      - traefik.enable=true
      - traefik.port=8761
    restart: unless-stopped
    networks:
      net:
        ipv4_address: "*************"

  api-gateway:
    image: tinashemk/myrepository:api-gateway
    container_name: api-gateway
    environment:
      - "SPRING_PROFILES_ACTIVE=dev"
    labels:
      - traefik.frontend.rule=Host:test-api.myworklink.uk
      - traefik.enable=true
      - traefik.port=8765
    restart: unless-stopped
    networks:
      net:
        ipv4_address: "*************"



  worklink-service:
    image: tinashemk/myrepository:worklink-service
    container_name: worklink-service
    restart: unless-stopped
    environment:
      - "SPRING_PROFILES_ACTIVE=dev"
    ports:
      - '8300:8300'
    volumes:
      - worklink_logs:/opt/worklink-service/logs
    networks:
      net:
        ipv4_address: "*************"


  forms-service:
    image: tinashemk/myrepository:forms-service
    container_name: forms-service
    restart: unless-stopped
    environment:
      - "SPRING_PROFILES_ACTIVE=dev"
    volumes:
      - forms_logs:/opt/forms-service/logs
    ports:
      - '8307:8307'
    networks:
      net:
        ipv4_address: "*************"

  mariadb:
    image: mariadb:latest
    container_name: mariadb-db
    ports:
      - 3303:3306
    environment:
      - "MARIADB_ROOT_PASSWORD=skdcnwauicn2ucnaecasdsajdnizucawencascdca"
      - "MARIADB_USER=worklink"
      - "MARIADB_PASSWORD=Worklink?1986"
      - "MARIADB_EXTRA_FLAGS=--max-connect-errors=1000 --max_connections=300"
      - "MARIADB_DATABASE=mydb"
    restart: on-failure
    volumes:
      - mariadb-data:/var/lib/mysql
      - mariadb-configs:/etc/mysql/conf.d
    networks:
      net:
        ipv4_address: "*************"

  db_admin:
    depends_on:
      - mariadb
    container_name: db_admin
    image: phpmyadmin/phpmyadmin
    labels:
      - traefik.port=80
      - traefik.enable=true
    environment:
      - "PMA_HOST=mariadb"
      - "MYSQL_ROOT_PASSWORD=skdcnwauicn2ucnaecasdsajdnizucawencascdca"
    ports:
      - "8084:80"
    networks:
      net:
        ipv4_address: "*************"

networks:
  net:
    driver: bridge
    ipam:
      config:
        - subnet: ***********/24
volumes:
  mariadb-data:
    driver: local
  mariadb-configs:
  worklink_logs:
  user_logs:
  ouath_logs:
  forms_logs:

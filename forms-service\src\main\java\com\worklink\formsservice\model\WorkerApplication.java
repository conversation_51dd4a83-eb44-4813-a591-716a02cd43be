package com.worklink.formsservice.model;

import com.worklink.formsservice.enums.FormStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import javax.persistence.*;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.Set;


@Entity
@AllArgsConstructor
@Data
public class WorkerApplication extends AbstractAuditingEntity{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String position;
    private String location;
    private String hours;
    private LocalDate closingDate;
    private String title;
    private String lastname;
    private String firstname;
    private String preferredName;
//    private String nationality;
    private LocalDate dob;
    private String address;
    private String email;
    private String phone;
    private Boolean ukWork;
    private String restrictionRes;
    private String insuaranceNum;
    @Column(length=5500)
    private String personalState;
    private String offensesRes;
    private String investigationsRes;
    private Boolean dbsCert;
    private Boolean dbs;
    private Boolean confidential;
    private LocalDate dbsRenewal;
    private String signed;
    private String printName;
    private LocalDate signDate;
    private Boolean licence;
    private Boolean car;
    private Boolean onlyEmployment;
    private String sanctionsRes;
    private String dismissedRes;
    @Column(length=2500)
    private String relatedRes;
    private String genFullname;
    private Boolean genSigned;
    private LocalDate genDate;
    private String unavailable;
    private Boolean submitted;

    private Boolean restriction;
    private Boolean offenses;
    private Boolean investigations;
    private Boolean availability;
    private Boolean sanctions;
    private Boolean dismissed;
    private Boolean related;

    private String dbsNumber;
    private String dbsType;

    private String profreg;

    private String certificate;

    private String lastModifiedDate1;

    @OneToMany(cascade = CascadeType.ALL)
    private Set<Education> educations = new HashSet<>();

    @OneToMany(cascade = CascadeType.ALL)
    private Set<Reference> references = new HashSet<>();

    @OneToMany(cascade = CascadeType.ALL)
    private Set<Employment> employments = new HashSet<>();

    @OneToMany(cascade = CascadeType.ALL)
    private Set<Employer> employers = new HashSet<>();


    @Column(unique = true, nullable = false)
    private Long workerId;

    public WorkerApplication() {};


}

package com.cap10mycap10.worklinkservice.controller;

import com.cap10mycap10.worklinkservice.model.Currency;
import com.cap10mycap10.worklinkservice.service.CurrencyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/v1/currencies")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class CurrencyController {

    private final CurrencyService currencyService;

    /**
     * Get all active currencies
     */
    @GetMapping("/active")
    public ResponseEntity<List<Currency>> getActiveCurrencies() {
        List<Currency> currencies = currencyService.getActiveCurrencies();
        return ResponseEntity.ok(currencies);
    }

    /**
     * Get all currencies
     */
    @GetMapping
    public ResponseEntity<List<Currency>> getAllCurrencies() {
        List<Currency> currencies = currencyService.getAllCurrencies();
        return ResponseEntity.ok(currencies);
    }

    /**
     * Get currency by code
     */
    @GetMapping("/{code}")
    public ResponseEntity<Currency> getCurrency(@PathVariable String code) {
        Optional<Currency> currency = currencyService.getCurrencyByCode(code);
        return currency.map(ResponseEntity::ok)
                      .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Create a new currency
     */
    @PostMapping
    public ResponseEntity<Currency> createCurrency(@RequestBody CreateCurrencyRequest request) {
        try {
            Currency currency = currencyService.createCurrency(
                request.getCode(),
                request.getName(),
                request.getSymbol()
            );
            return ResponseEntity.ok(currency);
        } catch (Exception e) {
            log.error("Failed to create currency: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Update currency status
     */
    @PutMapping("/{code}/status")
    public ResponseEntity<Currency> updateCurrencyStatus(
            @PathVariable String code,
            @RequestBody UpdateCurrencyStatusRequest request) {
        try {
            Currency currency = currencyService.updateCurrencyStatus(code, request.isActive());
            return ResponseEntity.ok(currency);
        } catch (Exception e) {
            log.error("Failed to update currency status: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Initialize default currencies
     */
    @PostMapping("/initialize")
    public ResponseEntity<String> initializeDefaultCurrencies() {
        try {
            currencyService.initializeDefaultCurrencies();
            return ResponseEntity.ok("Default currencies initialized successfully");
        } catch (Exception e) {
            log.error("Failed to initialize default currencies: {}", e.getMessage());
            return ResponseEntity.badRequest().body("Failed to initialize currencies: " + e.getMessage());
        }
    }

    // Request DTOs
    public static class CreateCurrencyRequest {
        private String code;
        private String name;
        private String symbol;

        // Getters and setters
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getSymbol() { return symbol; }
        public void setSymbol(String symbol) { this.symbol = symbol; }
    }

    public static class UpdateCurrencyStatusRequest {
        private boolean active;

        // Getters and setters
        public boolean isActive() { return active; }
        public void setActive(boolean active) { this.active = active; }
    }
}
